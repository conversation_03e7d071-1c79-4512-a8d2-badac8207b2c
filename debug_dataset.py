#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集诊断脚本
用于检查 pkl 文件的内容和结构，诊断 data_list 为空的问题
"""

import mmengine
import os
from typing import Dict, List, Any
from mmengine.logging import <PERSON><PERSON><PERSON><PERSON>

def analyze_pkl_file(pkl_path: str) -> None:
    """分析 pkl 文件的内容和结构"""
    print(f"=== 分析文件: {pkl_path} ===")
    
    # 检查文件是否存在
    if not os.path.exists(pkl_path):
        print(f"❌ 文件不存在: {pkl_path}")
        return
    
    print(f"✅ 文件存在，大小: {os.path.getsize(pkl_path) / 1024 / 1024:.2f} MB")
    
    try:
        # 加载 pkl 文件
        data = mmengine.load(pkl_path)
        print(f"✅ 文件加载成功")
        print(f"📊 数据类型: {type(data)}")
        
        if isinstance(data, dict):
            print(f"📋 字典键: {list(data.keys())}")
            
            # 检查 split 信息
            if 'split' in data:
                split_info = data['split']
                print(f"🔄 Split 信息:")
                print(f"   类型: {type(split_info)}")
                if isinstance(split_info, dict):
                    for split_name, split_data in split_info.items():
                        print(f"   - {split_name}: {len(split_data)} 项")
                        if len(split_data) > 0:
                            print(f"     示例: {list(split_data)[:3]}...")
                else:
                    print(f"   内容: {split_info}")
            else:
                print("❌ 缺少 'split' 键")
            
            # 检查 annotations 信息
            if 'annotations' in data:
                annotations = data['annotations']
                print(f"📝 Annotations 信息:")
                print(f"   类型: {type(annotations)}")
                print(f"   数量: {len(annotations)}")
                
                if len(annotations) > 0:
                    first_anno = annotations[0]
                    print(f"   第一个样本的键: {list(first_anno.keys())}")
                    print(f"   第一个样本示例:")
                    for key, value in first_anno.items():
                        if isinstance(value, (list, tuple)) and len(value) > 5:
                            print(f"     {key}: {type(value)} (长度: {len(value)})")
                        else:
                            print(f"     {key}: {value}")
                    
                    # 统计标签分布
                    label_counts = {}
                    identifier_counts = {'filename': 0, 'frame_dir': 0}
                    
                    for anno in annotations:
                        # 统计标签
                        if 'label' in anno:
                            label = anno['label']
                            label_counts[label] = label_counts.get(label, 0) + 1
                        
                        # 统计标识符
                        if 'filename' in anno:
                            identifier_counts['filename'] += 1
                        if 'frame_dir' in anno:
                            identifier_counts['frame_dir'] += 1
                    
                    print(f"   标签分布: {label_counts}")
                    print(f"   标识符统计: {identifier_counts}")
                    
                else:
                    print("❌ annotations 为空")
            else:
                print("❌ 缺少 'annotations' 键")
                
        elif isinstance(data, list):
            print(f"📋 列表长度: {len(data)}")
            if len(data) > 0:
                print(f"📝 第一个元素类型: {type(data[0])}")
                if isinstance(data[0], dict):
                    print(f"   第一个元素的键: {list(data[0].keys())}")
        else:
            print(f"⚠️  未知数据格式: {type(data)}")
            
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        import traceback
        traceback.print_exc()

def test_dataset_loading(pkl_path: str, split: str = 'train') -> None:
    """测试数据集加载过程"""
    print(f"\n=== 测试数据集加载 (split={split}) ===")
    
    try:
        from mmaction.datasets.multimodal_dataset import PoseRgbDataset
        
        # 创建数据集实例（不需要 pipeline）
        dataset = PoseRgbDataset(
            ann_file=pkl_path,
            pipeline=[],  # 空 pipeline 用于测试
            split=split
        )
        
        print(f"✅ 数据集创建成功")
        print(f"📊 data_list 长度: {len(dataset.data_list)}")
        
        if len(dataset.data_list) > 0:
            print(f"📝 第一个样本: {dataset.data_list[0]}")
            
            # 测试 get_class_counts
            class_counts = dataset.get_class_counts()
            print(f"📈 类别统计: {class_counts}")
        else:
            print("❌ data_list 为空")
            
    except Exception as e:
        print(f"❌ 数据集加载失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    # 配置文件中的路径
    pkl_files = [
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/trainval_yolopose.pkl',
        '/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets/test_trainval_yolopose.pkl'
    ]
    
    for pkl_path in pkl_files:
        analyze_pkl_file(pkl_path)
        
        # 测试不同的 split
        for split in ['train', 'val', None]:
            test_dataset_loading(pkl_path, split)
        
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main()
