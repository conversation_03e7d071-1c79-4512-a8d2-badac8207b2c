# -*-coding:utf-8-*-
"""
type_file = list(Path(pth).rglob('*.*'))
优化速度:
    # 方案A：多线程搜索
    from ReadTypeFile_Speed import rglob_optimized
    type_file = rglob_optimized(path1, '*.pkl', max_workers=4)
优化内存：
    # 方案A+: 生成器 边读边处理
"""
from pathlib import Path
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Generator
import threading


class OptimizedFileSearcher:
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self._lock = threading.Lock()

    def find_files_parallel(self, root_path: str, pattern: str = '*.pkl') -> List[Path]:
        """
        使用多线程 + os.scandir 优化的文件搜索
        保留 pathlib 的返回格式

        Args:
            root_path: 搜索根路径
            pattern: 文件模式，如 '*.pkl'

        Returns:
            List[Path]: 找到的文件路径列表
        """
        root_path = Path(root_path)
        extension = pattern.replace('*', '')  # 提取扩展名，如 '.pkl'

        # 收集所有目录
        directories = self._collect_directories(root_path)

        # 多线程搜索文件
        all_files = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交搜索任务
            futures = {
                executor.submit(self._search_directory, directory, extension): directory
                for directory in directories
            }

            for future in as_completed(futures):
                # 这里可以进行异常处理
                files = future.result()
                all_files.extend(files)

        return all_files

    def _collect_directories(self, root_path: Path) -> List[Path]:
        """收集所有需要搜索的目录"""
        directories = [root_path]

        try:
            # 使用 os.scandir 快速收集目录
            def _collect_recursive(path: Path):
                try:
                    with os.scandir(str(path)) as entries:
                        for entry in entries:
                            if entry.is_dir():
                                dir_path = Path(entry.path)
                                directories.append(dir_path)
                                _collect_recursive(dir_path)
                except (PermissionError, OSError):
                    pass

            _collect_recursive(root_path)
        except Exception:
            pass

        return directories

    def _search_directory(self, directory: Path, extension: str) -> List[Path]:
        """在单个目录中搜索指定扩展名的文件"""
        files = []
        try:
            with os.scandir(str(directory)) as entries:
                for entry in entries:
                    if entry.is_file() and entry.name.endswith(extension):
                        files.append(Path(entry.path))
        except (PermissionError, OSError):
            pass
        return files


# 方案A: 通用函数
def rglob_optimized(root_path: str, pattern: str = '*.pkl', max_workers: int = 4) -> List[Path]:
    """
    优化版本的 rglob，支持不同文件类型

    使用方法：
    # pkls = rglob_optimized(path1, '*.pkl')
    # txts = rglob_optimized(path1, '*.txt')
    """
    searcher = OptimizedFileSearcher(max_workers=max_workers)
    return searcher.find_files_parallel(root_path, pattern)


# 方案A+: 生成器版本（节省内存）
def rglob_optimized_generator(root_path: str, pattern: str = '*.pkl',
                              max_workers: int = 4) -> Generator[Path, None, None]:
    """
    生成器版本，适合处理大量文件时节省内存

    使用方法：
    for pkl_file in rglob_optimized_generator(path1):
        process(pkl_file)
    """
    files = rglob_optimized(root_path, pattern, max_workers)
    for file in files:
        yield file


# 使用示例
if __name__ == "__main__":
    # 原代码改动最小的方式
    path1 = "/your/target/path"

    # 方法2: 通用函数
    pkls = rglob_optimized(path1, '*.pkl', max_workers=4)


    # 方法4: 生成器方式（推荐大文件量时使用）
    for pkl_file in rglob_optimized_generator(path1, '*.pkl', max_workers=4):
        print(f"Found: {pkl_file}")
        # 处理单个文件...