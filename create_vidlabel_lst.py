# -*-coding:utf-8-*-
"""
50m项目：创建 vid跑道标签 载入样本pkl
SitUp Fusion项目：创建 vid pkl标签
1. 按pkl名生成标签，划分训练和验证
"""
import random
import argparse
from tqdm import tqdm
import time
from pathlib import Path
from ReadTypeFile_Speed import rglob_optimized

import os
from concurrent.futures import ProcessPoolExecutor
from pathlib import Path
from typing import List, Set, Generator, Tuple
import multiprocessing as mp


def read_labels(label_pth):
    """
    获取标签信息，与文件夹关联
    """
    with open(label_pth, 'r') as flab:
        labels = [line.strip() for line in flab.readlines() if len(line) > 1]

    return labels


def _process_single_directory(args: Tuple[str, Set[str]]) -> List[str]:
    """处理单个目录的全局函数"""
    directory, extensions = args
    local_files = []

    for dirpath, dirnames, filenames in tqdm(os.walk(directory), desc=f'a_dir {Path(directory).name}'):
        dirnames[:] = [d for d in dirnames if not d.startswith('.')]
        for filename in filenames:
            if any(filename.lower().endswith(ext) for ext in extensions):
                local_files.append(os.path.join(dirpath, filename))

    return local_files


def find_files_multiprocess(root_directory: str, file_extensions: List[str], max_workers: int = None) -> List[str]:
    """
    多进程超高速文件搜索 - 适合大型目录

    Args:
        root_directory: 根目录路径
        file_extensions: 文件扩展名列表
        max_workers: 最大进程数，默认为CPU核心数

    Returns:
        匹配文件的完整路径列表
    """
    if not os.path.exists(root_directory):
        return []

    # 预处理扩展名
    extensions = {ext.lower() if ext.startswith('.') else f'.{ext.lower()}' for ext in file_extensions}
    max_workers = max_workers or min(mp.cpu_count(), 8)  # 限制最大进程数

    # 获取顶层目录
    # try:
    with os.scandir(root_directory) as entries:
        top_dirs = [entry.path for entry in entries if entry.is_dir() and not entry.name.startswith('.')]
        root_files = []

        # 处理根目录文件
        for entry in entries:
            if entry.is_file() and any(entry.name.lower().endswith(ext) for ext in extensions):
                root_files.append(os.path.join(root_directory, entry.name))
    # except (PermissionError, OSError):
    #     return []

    if not top_dirs:
        return root_files

    # 准备多进程参数
    args_list = [(directory, extensions) for directory in top_dirs]
    all_files = root_files[:]

    # try:
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        results = executor.map(_process_single_directory, args_list)
        for result in tqdm(results, desc='Process_res'):
            all_files.extend(result)
    # except Exception as e:
    #     print(f"多进程处理出错，回退到单线程: {e}")
    #     # 回退到单线程处理
    #     for directory in top_dirs:
    #         try:
    #             for dirpath, dirnames, filenames in os.walk(directory):
    #                 dirnames[:] = [d for d in dirnames if not d.startswith('.')]
    #                 for filename in filenames:
    #                     if any(filename.lower().endswith(ext) for ext in extensions):
    #                         all_files.append(os.path.join(dirpath, filename))
    #         except (PermissionError, OSError):
    #             continue

    return all_files


class video_data:
    def __init__(self, val_rate, suffix='pkl', dataset_dir='datasets'):
        self.suffix = suffix
        self.val_rate = val_rate            # 验证集比例 默认0.2
        self.dataset_dir = dataset_dir

    def convert_dataset(self, lable_name, alabel_lst, txts_path_tup, SclName='Scl'):
        all_txt_path, train_txt_path, val_txt_path = txts_path_tup
        train_txt = open(train_txt_path, "a")
        val_txt = open(val_txt_path, "a")
        all_txt = open(all_txt_path, "a")

        # 按类别 划分训练、验证
        all_num = len(alabel_lst)
        all_list = range(all_num)
        # 这里按照2级别文件夹进行随机划分
        val_num = random.sample(all_list, int(self.val_rate * all_num))  # 随机抽取数据集的rate 做验证集
        for ik, vid_pth in enumerate(tqdm(alabel_lst, desc=f'Data {SclName}')):
            if not list(Path(vid_pth).parent.glob((Path(vid_pth).stem + '-*.jpg'))).__len__() == 3:
                continue
                # f"@Moss: Check {vid_pth} and its dir with JPG num is not 3"

            vid_pth = str(vid_pth)
            all_txt.writelines(vid_pth + " " + str(lable_name) + "\n")
            if ik in val_num:
                val_txt.writelines(vid_pth + " " + str(lable_name) + "\n")
            else:
                train_txt.writelines(vid_pth + " " + str(lable_name) + "\n")            #

        train_txt.close()
        val_txt.close()
        all_txt.close()

    def videos_files_convert(self, data_files_path, label_list, TestMod=False, default_label=None):
        if self.dataset_dir == "Fusion_datasets":
            dataset_txt_path = Path(data_files_path).parent / self.dataset_dir               # "datasets"
        else:
            dataset_txt_path = Path(self.dataset_dir)
        Path(dataset_txt_path).mkdir(exist_ok=True)

        if TestMod:
            txt_names = ["test_all_label.txt", "test_train_label.txt", "test_val_label.txt"]
        else:
            txt_names = ["all_label.txt", "train_label.txt", "val_label.txt"]

        train_txt_path = dataset_txt_path / txt_names[1]
        val_txt_path = dataset_txt_path / txt_names[2]
        all_txt_path = dataset_txt_path / txt_names[0]
        txts_path_tup = (all_txt_path, train_txt_path, val_txt_path)
        open(train_txt_path, 'w').close()
        open(val_txt_path, 'w').close()
        open(all_txt_path, 'w').close()

        if default_label is not None:
            lable_name = default_label              # 测试模式
            for scl_dir in Path(data_files_path).glob('*'):
                if not scl_dir.is_dir():
                    continue
                # alabel_lst = [str(f) for f in scl_dir.rglob(f'*.{self.suffix}')]        # 类别下的所有样本
                alabel_lst = rglob_optimized(scl_dir, f'*.{self.suffix}', max_workers=4)

                self.convert_dataset(lable_name, alabel_lst, txts_path_tup, SclName=scl_dir.name)
        else:

            for i, lable_name in enumerate(label_list):
                label_num = 0
                print(lable_name)
                video_files_path = Path(data_files_path) / lable_name
                if not video_files_path.exists():
                    continue
                Scls_lst = [d for d in video_files_path.iterdir() if d.is_dir()]        # 类别下的所有文件夹
                start = time.perf_counter()
                for scl_dir in Scls_lst:
                    alabel_lst = find_files_multiprocess(scl_dir, [f'.{self.suffix}'])        # 类别下的所有样本
                    # alabel_lst = [str(f) for f in tqdm(scl_dir.rglob(f'*.{self.suffix}'))]        # 类别下的所有样本


                    label_num += len(alabel_lst)
                    self.convert_dataset(lable_name, alabel_lst, txts_path_tup, SclName=scl_dir.name)

                print(f"num is {label_num}")
                end = time.perf_counter()
                print(f"耗时 {end - start:.4f} 秒")



if __name__ == '__main__':
    parser = argparse.ArgumentParser(prog='训练-验证标签生成, 默认训练集')
    parser.add_argument('--data_pth', type=str,
                        default="/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb", help='训练集标签来源')
                        # default="/root/share175/sport_test/sit_up/classify_cheat/pose_rgb", help='测试集标签来源')
                        # default="/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/2_headErr/HeadErr_scls/Hubei_wuhangongshangxueyuan_Model", help='训练集标签来源')
    parser.add_argument('--label_name', default="labels.txt", help='标签名在数据路径中，并与文件夹对应')
    parser.add_argument('--val_rate', default=0.15, help='验证集占整个训练集的比例')
    parser.add_argument('--dataset_dir', default='Fusion_datasets', help='txt保存文件夹名, 默认 Fusion_datasets')

    parser.add_argument('--TestMod', type=str, default=False, help='测试集模式')
    parser.add_argument('--default_label', default=None, help='测试模式下默认标签， 默认None')


    opt = parser.parse_args()

    opt.val_rate = float(opt.val_rate)
    if isinstance(opt.TestMod, str):
        opt.TestMod = opt.TestMod.lower() == 'true'  # True 或 true

    if opt.TestMod:
        opt.val_rate = 1.0
        label_pth = "/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/labels.txt"
    else:
        label_pth = Path(opt.data_pth) / opt.label_name

    labels_lst = read_labels(label_pth)
    print(labels_lst)

    video_data = video_data(val_rate=opt.val_rate, dataset_dir=opt.dataset_dir)
    video_data.videos_files_convert(opt.data_pth, labels_lst, opt.TestMod, opt.default_label)
