场景描述：
1. 仰卧起坐场景理解与作弊违规检测：
通过3分类模型，识别单次仰卧其坐过程中的 正常、作弊行为 与 过程中未抱头行为(包含单手抱头)
(1) 主要的作弊行为: 借助外力作弊(将1只手藏在摄像头盲区, 拉拽衣脚起身; 借助绳子、衣服等工具拉拽人体完成仰卧起坐的作弊行为; 推拉扶他人配合动作作弊；
(2) 主要的未抱头行为: 仰卧起坐从起身开始到促膝，过程中存在任1只手未抱头的行为
(3) 正常单次仰卧起坐: 从躺平到起身到促膝，全程双手抱头
2. 样本描述：
单个样本包含3张裁剪后的RGB图像和仰卧起坐主体的整个仰卧起坐动作(抽样得到固定时长帧)
(1) 3张裁剪图像来整个仰卧起坐动作第1帧(躺平)，中间帧(固定人体倾角90度附近)， 结束帧(促膝)， 即 (3帧 * H0，W0，3);
(1.1) 裁剪区域为原画面基于人体框脚的一边作为边界，另一边以人体框边界外扩(肩点到人体框边界的距离) 为边界；下边界为画面底边，上边界以整个动作过程人体框的最高点作为上边界, 得到((3帧 * H1，W2，3))；
(1.2) 通过对H1， W1 进行Resize和Padding操作，得到固定尺寸的图像输入(3, H=224, W=224, 3)
(2) 整个仰卧起坐动作过程(假设共tm帧)，取每帧的主体的17个COCO格式的2D骨骼点(x, y)，tm帧抽样得到Frame=35帧，Frame * 17*2
故，单个样本的尺寸为 图像序列:(3, H=224, W=224, 3) 和 骨骼序列 (Frame=35, 17, 2)
3. 模型描述：
模型采用双模态融合特征分类模型, 分为4个部分：
(1) 骨骼模块: 采用ST-GCN的特征层，提取骨骼的时序特征(主要想要获取正常动作和过程中未抱头的运动特征)
(2) 图像模块: 采用TSM算法的特征层，提取时序图像的场景和时序特征(主要想获取借助工具等外力作弊的运动特征)
(3) 融合模块: 将(1)和(2)提取到的特征进行融合,以便获得1个特征输出
(3) 分类头: 3分类

训练相关参数配置：
默认配置文件路径：/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0807/configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py
骨骼图像双流数据集
⓪样本输入: 
Pose(Batch, Frame, 17, 3): 
(1) Frame = 35（对应 预处理 UniformSamplerames(clip_len=35)）
mRGB(Batch, Channel=3, frames=3, img_h=224, img_w=224)：
(1) img_h=224, img_w=224 (对应 预处理SimplyResize(scale=(224, 224)) )

②数据预处理:
默认使用 配置文件


③训练配置:
优化器：OptimWrapper(Adamw, lr=0.001, 'image_backbone': lr=1.0 * 0.1, 其他默认1.0)
梯度裁剪：
1. clip_grade(max_norm=20, norm_type=2)    #  L2范数
2. max_norm=10
优化策略: 
(1) 线性预热: LinearLR
(2) 余弦退火: CosineAnnealingLR


MultiModalRecognizer:
❶ [骨骼模块]
1. ST-GCN  pose_feature_dim=256 
假设ST-GCN模型有以下几层时间卷积，并且输入时间维度是 Frames=30：
第一层TCN：kernel_size=9, stride=1。输出时间维度不变，仍然是30。
第二层TCN：kernel_size=9, stride=2。时间维度减半，ceil(30 / 2) = 15。输出时间维度变为 15。
第三层TCN：kernel_size=9, stride=2。时间维度再次减半，ceil(15 / 2) = 8。输出时间维度变为 8。

❷ [图像模块]
1. TSM-MobilenetV2 img_feat_dim=320
2. TSM-Reset50  img_feat_dim=512

❸ [特征融合模块]
1. cross_model: 跨模态交互融合（图像<-(注意力投影)->姿态）  
2. attention: 标准注意力融合 (时序堆叠+M-atten+平均池化)
3. temporal_attention: 时序感知注意力(时序编码(模拟时序信息) + M-atten)

以上模型配置默认使用第1条，例如 ❸ [特征融合模块] 使用的是 1.cross_model

实验记录：
以下 修改配置 列的内容表示为基于baseLine(实验4)内容修改的相关参数，例如：
实验2表示 在实验4的配置基础上 将 ❷ 图像模块 修改为 ②-2 TSM-Reset50
实验5表示 在实验4的配置基础上 将 ❸ [特征融合模块] 修改为 ③-2 attention

测试集(异常检测) fake_det 表示 作弊或未抱头 均检测为异常 的准确率
测试集(异常检测)  Norm_errRate 表示 正常误判为 作弊或未抱头 的错误率
2分类Acc 表示将 作弊和未抱头 合并为1类，检测准确率
以下实验的训练记录保存在 /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0807/tools/work_dirs/pose_rgb_fusion 路径下，备注中有具体文件夹，请仔细阅读和分析

| 实验 | Train-Date | PoseRGB 数据量 | Samples Testsets | 修改配置                               | 测试集(异常检测) fake_det :  Norm_errRate | 3分类平均Acc : 2分类Acc | 参数和训练记录文件夹                                              |
|:---|:-----------|:------------|:-----------------|:-----------------------------------|:----------------------------------:|:------------------|:--------------------------------------------------------|
| 1  | 2025/7/30  | ~1.5W       | 3161             | -                                  |           0.9653 : 1.65%           | 0.92 : 0.9418     | tools/work_dirs/pose_rgb_fusion/20250730_121806_padding |
| 2  | 2025/8/1   | 1.5~1.6W    | 3161             | ❷-2 TSM-Reset50 512                |           0.9461 : 2.3%            | 0.94 : 0.9668     | tools/work_dirs/pose_rgb_fusion/20250801_100506_R50_512 |
| 3  | 2025/8/5   | ~1.6W       | 3161             | -                                  |           0.9418 : 4.52%           | 0.92 : 0.9418     | tools/work_dirs/pose_rgb_fusion/20250805_045639         |
| 4  | 2025/8/6   | 1.65W       | 3161             | baseLine cross_model : padding Img |           0.9383 : 5.09%           | 0.91 : 0.9456     | tools/work_dirs/pose_rgb_fusion/20250806_045654         |
| 5  |            | 1.65W       | 3161             | ❸-2 attention                      |           0.9499 : 4.52%           | 0.92 : 0.9532     | tools/work_dirs/pose_rgb_fusion/20250806_072035_atten   |
| 6  |            | 1.65W       | 3161             | ❸-3 temporal_attention             |               训练未拟合                | -                 |                                                         |
| 7  | 2025/8/7   | 1.65W       | 3161             | ❸-2 attention   ③-2 max_norm=10    |  验证集acc/top1 0.9026, 测试3分类acc 10%  | -                 |                                                         |
目前1.65W的训练数据中，正常类别样本数是6258, 作弊样本数5494, 未抱头样本数4751,
注意，实验1的训练数据量是小于实验5的，最公平的比较是实验4和实验5, 实验4到实验5只修改了attention融合，而实验1和实验4的差别仅是数据量的差别，配置都是cross_model


需求和问题：
1. 从初步的训练和测试实验结果来看，目前配置下最优的作弊检出率是来自实验1的配置，异常检出率0.9653, 正常误判率1.65%， 在当前训练集下, 我想要提高这个指标,我应该如何调整参数进行后续的实验验证；
2. 请结合相关模型调优的知识，在当前场景下(训练和测试数据来自几十所不同中小学的真实仰卧起坐动作数据)，如何进行优化；
3. 我的训练框架即为当前的项目路径 /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0807， 训练采用的4卡，训练脚本 tools/train_m2.py
4. 当前模型使用的是 mmaction/models/recognizers/multimodal_recognizer.py
5. 我从完整的训练和测试数据中随机抽出了部分样本作为参考，路径在 /media/pyl/WD_Blue_1T/All_proj/pose_rgb, 每个pkl所在的文件夹包含的所有内容为1个样本(包含1个pkl记录骨骼数据, 3张裁剪后的RGB图片, 1个avi视频标签用于判断样本属于哪个类别)
6. 给出方案及构思时，不要局限于我已进行的实验，我已进行的实验仅供参考，仅表示我在当前训练配置下的状态
7. 注意，后续训练数据会逐步增加，因此方案中不要出现样本的具体数量，而是用变量代替
