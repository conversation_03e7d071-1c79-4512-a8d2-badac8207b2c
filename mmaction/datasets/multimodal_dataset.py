# -*-coding:utf-8-*-
from typing import Callable, Dict, List, Optional, Union
import os.path as osp

import mmengine
from mmengine.logging import MMLogger

import mmcv
import numpy as np
from mmaction.registry import DATASETS
from mmaction.datasets.base import BaseActionDataset


@DATASETS.register_module()
class PoseRgbDataset(BaseActionDataset):
    """双模态数据集，包含姿态点序列和图像"""

    def __init__(self,
                 ann_file,
                 pipeline: List[Union[Dict, Callable]],
                 split: Optional[str] = None,
                 valid_ratio: Optional[float] = None,
                 **kwargs):
        self.split = split
        self.valid_ratio = valid_ratio

        logger = MMLogger.get_current_instance()
        logger.info(f"=== PoseRgbDataset.__init__ called ===")
        logger.info(f"ann_file: {ann_file}")
        logger.info(f"split: {split}")
        logger.info(f"valid_ratio: {valid_ratio}")
        logger.info(f"Dataset instance id: {id(self)}")

        # print(ann_file)
        super().__init__(ann_file, pipeline=pipeline, modality='Pose', **kwargs)

        logger.info(f"=== PoseRgbDataset.__init__ completed ===")
        logger.info(f"Final data_list length: {len(getattr(self, 'data_list', []))}")
        logger.info(f"Dataset instance id after init: {id(self)}")

    def load_annotations(self):
        """加载标注文件"""
        video_infos = []
        with open(self.ann_file, 'r') as f:
            for line in f:
                line_split = line.strip().split()
                video_info = dict()

                # 解析数据路径和标签
                video_info['pose_file'] = line_split[0]  # 姿态点文件路径
                video_info['img_file'] = line_split[1]  # 图像文件路径
                video_info['label'] = int(line_split[2])  # 标签

                # 设置帧数等信息
                video_info['total_frames'] = 40
                video_info['start_index'] = 0

                video_infos.append(video_info)

        return video_infos

    def prepare_train_frames(self, idx):
        """准备训练数据"""
        results = self.video_infos[idx].copy()
        results['modality'] = 'Pose+RGB'
        return self.pipeline(results)

    def prepare_test_frames(self, idx):
        """准备测试数据"""
        results = self.video_infos[idx].copy()
        results['modality'] = 'Pose+RGB'
        return self.pipeline(results)

    def load_data_list(self) -> List[Dict]:
        """Load annotation file to get skeleton information."""
        assert self.ann_file.endswith('.pkl'), f"@Moss: Check your pkl name"
        mmengine.exists(self.ann_file)

        logger = MMLogger.get_current_instance()
        logger.info(f"Loading data from: {self.ann_file}")

        data_list = mmengine.load(self.ann_file)
        logger.info(f"Raw data loaded, type: {type(data_list)}")

        if isinstance(data_list, dict):
            logger.info(f"Data dict keys: {list(data_list.keys())}")
            if 'split' in data_list:
                logger.info(f"Available splits: {list(data_list['split'].keys())}")
            if 'annotations' in data_list:
                logger.info(f"Total annotations: {len(data_list['annotations'])}")

        if self.split is not None:
            logger.info(f"Filtering data for split: {self.split}")

            # 容错处理：检查数据格式
            if not isinstance(data_list, dict):
                logger.error(f"Invalid data format. Expected dict, got: {type(data_list)}")
                # 如果是列表格式，直接返回
                if isinstance(data_list, list):
                    logger.warning("Data is in list format, ignoring split parameter")
                    return data_list
                return []

            # 容错处理：如果没有 split 信息，返回所有 annotations
            if 'split' not in data_list:
                logger.warning("No 'split' key found in data, ignoring split parameter")
                if 'annotations' in data_list:
                    logger.info(f"Using all {len(data_list['annotations'])} annotations")
                    return data_list['annotations']
                else:
                    logger.error("No 'annotations' key found in data")
                    return []

            if 'annotations' not in data_list:
                logger.error("No 'annotations' key found in data")
                return []

            split, annos = data_list['split'], data_list['annotations']

            # 容错处理：如果指定的 split 不存在，尝试使用可用的 split
            if self.split not in split:
                logger.error(f"Split '{self.split}' not found in available splits: {list(split.keys())}")
                if split:
                    fallback_split = list(split.keys())[0]
                    logger.warning(f"Using fallback split: {fallback_split}")
                    split_set = set(split[fallback_split])
                else:
                    logger.error("No splits available")
                    return []
            else:
                split_set = set(split[self.split])

            if len(annos) == 0:
                logger.warning("No annotations found in data file")
                return []

            identifier = 'filename' if 'filename' in annos[0] else 'frame_dir'
            logger.info(f"Using identifier: {identifier}")
            logger.info(f"Split contains {len(split_set)} items")

            # dict_keys(['keypoint', 'keypoint_score', 'img_shape', 'original_shape', 'total_frames', 'vid_frames', 'frame_dir', 'filename', 'label'])
            data_list = [x for x in annos if x[identifier] in split_set]            # 划分训练、验证
            logger.info(f"After filtering: {len(data_list)} samples remain")
        else:
            annos = data_list['annotations'] if isinstance(data_list, dict) else data_list
            data_list = [x for x in annos]
            logger.info(f"No split specified, using all {len(data_list)} samples")

        # print('*'*40, self.split, data_list.__len__(), split)
        return data_list

    def filter_data(self) -> List[Dict]:
        """Filter out invalid samples."""
        logger = MMLogger.get_current_instance()
        logger.info(f"Starting filter_data with {len(self.data_list)} samples")

        # 检查是否需要进行有效性过滤
        if self.valid_ratio is not None and isinstance(
                self.valid_ratio, float) and self.valid_ratio > 0:
            logger.info(f"Applying valid_ratio filter: {self.valid_ratio}")

            # 检查数据是否包含必要的字段
            if len(self.data_list) > 0:
                sample = self.data_list[0]
                has_valid = 'valid' in sample
                has_box_score = 'box_score' in sample
                has_total_frames = 'total_frames' in sample

                logger.info(f"Sample fields check - valid: {has_valid}, box_score: {has_box_score}, total_frames: {has_total_frames}")

                if not (has_valid and has_total_frames):
                    logger.warning("Samples missing required fields for valid_ratio filtering. Skipping filter.")
                else:
                    # 检查 box_thr 属性
                    if not hasattr(self, 'box_thr'):
                        logger.warning("box_thr not defined, using default value 0.5")
                        self.box_thr = 0.5

                    # 进行过滤
                    original_count = len(self.data_list)
                    try:
                        self.data_list = [
                            x for x in self.data_list
                            if 'valid' in x and self.box_thr in x['valid'] and
                            x['valid'][self.box_thr] / x['total_frames'] >= self.valid_ratio
                        ]
                        logger.info(f"Valid ratio filtering: {original_count} -> {len(self.data_list)} samples")

                        # 处理 box_score 和 anno_inds
                        if has_box_score:
                            for item in self.data_list:
                                if 'box_score' in item:
                                    anno_inds = (item['box_score'] >= self.box_thr)
                                    item['anno_inds'] = anno_inds
                        else:
                            logger.warning("Samples missing 'box_score' field, skipping anno_inds generation")

                    except Exception as e:
                        logger.error(f"Error during valid_ratio filtering: {e}")
                        logger.warning("Keeping original data without filtering")
        else:
            logger.info("No valid_ratio filtering applied")

        logger.info(f'{len(self.data_list)} videos remain after valid thresholding')
        return self.data_list

    def get_class_counts(self) -> List[int]:
        """
        获取各类别的样本数量，用于动态计算类别权重

        Returns:
            List[int]: 各类别样本数量 [normal_count, fake_count, head_err_count]
        """
        logger = MMLogger.get_current_instance()
        logger.info(f"=== get_class_counts called ===")
        logger.info(f"Dataset instance id: {id(self)}")
        logger.info(f"ann_file: {getattr(self, 'ann_file', 'NOT_SET')}")
        logger.info(f"split: {getattr(self, 'split', 'NOT_SET')}")

        if not hasattr(self, 'data_list') or not self.data_list:
            logger.warning(f"data_list is empty or not initialized. Length: {len(getattr(self, 'data_list', []))}")
            logger.warning("This might indicate a problem with data loading.")

            # 方法1：尝试重新加载数据
            logger.info("Attempting to reload data...")
            try:
                self.data_list = self.load_data_list()
                if self.data_list:
                    logger.info(f"Successfully reloaded {len(self.data_list)} samples")
                else:
                    logger.error("Data reload failed - still empty")
                    # 方法2：直接从原始文件统计
                    logger.info("Trying to count classes from raw data file...")
                    return self._count_classes_from_raw_data()
            except Exception as e:
                logger.error(f"Failed to reload data: {e}")
                # 方法2：直接从原始文件统计
                logger.info("Trying to count classes from raw data file...")
                return self._count_classes_from_raw_data()

        class_counts = [0, 0, 0]  # 初始化3个类别的计数
        invalid_labels = []

        for i, data_info in enumerate(self.data_list):
            if not isinstance(data_info, dict):
                logger.warning(f"Sample {i} is not a dict: {type(data_info)}")
                continue

            label = data_info.get('label', None)
            if label is None:
                logger.warning(f"Sample {i} missing 'label' key. Keys: {list(data_info.keys())}")
                continue

            if 0 <= label < len(class_counts):
                class_counts[label] += 1
            else:
                invalid_labels.append((i, label))

        if invalid_labels:
            logger.warning(f"Found {len(invalid_labels)} samples with invalid labels: {invalid_labels[:5]}...")

        logger.info(f'Class distribution: Normal={class_counts[0]}, '
                   f'Fake={class_counts[1]}, HeadErr={class_counts[2]}')
        logger.info(f'Total samples processed: {sum(class_counts)}')

        return class_counts

    def _count_classes_from_raw_data(self) -> List[int]:
        """
        直接从原始数据文件统计类别分布，用于 data_list 为空时的备用方案

        Returns:
            List[int]: 各类别样本数量 [normal_count, fake_count, head_err_count]
        """
        logger = MMLogger.get_current_instance()

        try:
            # 加载原始数据
            raw_data = mmengine.load(self.ann_file)

            if isinstance(raw_data, list):
                # 如果是简单列表格式
                annotations = raw_data
            elif isinstance(raw_data, dict) and 'annotations' in raw_data:
                # 如果是字典格式
                annotations = raw_data['annotations']
            else:
                logger.error(f"Cannot extract annotations from raw data format: {type(raw_data)}")
                return [0, 0, 0]

            # 统计类别分布
            class_counts = [0, 0, 0]
            for anno in annotations:
                if isinstance(anno, dict) and 'label' in anno:
                    label = anno['label']
                    if 0 <= label < len(class_counts):
                        class_counts[label] += 1

            logger.info(f"Counted classes from raw data: Normal={class_counts[0]}, "
                       f"Fake={class_counts[1]}, HeadErr={class_counts[2]}")

            return class_counts

        except Exception as e:
            logger.error(f"Failed to count classes from raw data: {e}")
            # 返回默认值
            default_counts = [6258, 5494, 4751]
            logger.warning(f"Using default class counts: {default_counts}")
            return default_counts

    def get_data_info(self, idx: int) -> Dict:
        """Get annotation by index."""
        data_info = super().get_data_info(idx)

        # print(f'@Moss: data_info, {idx}')
        # if 'imgs_pth_Resize' in data_info:
        #     imgs = [mmcv.imread(img_pth) for img_pth in data_info['imgs_pth_Resize']]
        #     data_info['imgs'] = imgs

        # breakpoint()
        # Sometimes we may need to load skeleton from the file
        if 'skeleton' in self.data_prefix:
            identifier = 'filename' if 'filename' in data_info else 'frame_dir'
            ske_name = data_info[identifier]
            ske_path = osp.join(self.data_prefix['skeleton'], ske_name + '.pkl')
            ske = mmengine.load(ske_path)
            for k in ske:
                data_info[k] = ske[k]

        return data_info



@DATASETS.register_module()
class LoadPose2D:
    """加载2D姿态序列数据"""

    def __init__(self,
                 pose_format='npy',
                 num_keypoints=17,
                 coord_dim=3):
        self.pose_format = pose_format
        self.num_keypoints = num_keypoints
        self.coord_dim = coord_dim

    def __call__(self, results):
        """加载姿态数据"""
        pose_file = results['pose_file']

        if self.pose_format == 'npy':
            pose_data = np.load(pose_file)  # (T, V, C)
        elif self.pose_format == 'json':
            # 处理JSON格式的姿态数据
            import json
            with open(pose_file, 'r') as f:
                pose_json = json.load(f)
            pose_data = self._parse_json_pose(pose_json)
        else:
            raise ValueError(f"Unsupported pose format: {self.pose_format}")

        # 验证数据格式
        assert pose_data.shape[1] == self.num_keypoints, \
            f"Expected {self.num_keypoints} keypoints, got {pose_data.shape[1]}"
        assert pose_data.shape[2] == self.coord_dim, \
            f"Expected {self.coord_dim} coordinates, got {pose_data.shape[2]}"

        results['keypoint'] = pose_data
        results['total_frames'] = pose_data.shape[0]

        return results

    def _parse_json_pose(self, pose_json):
        """解析JSON格式的姿态数据"""
        # 根据具体的JSON格式实现解析逻辑
        # 这里提供一个示例实现
        frames = []
        for frame_data in pose_json['annotations']:
            keypoints = np.array(frame_data['keypoints']).reshape(-1, 3)
            frames.append(keypoints)
        return np.array(frames)


@DATASETS.register_module()
class PoseNormalize:
    """姿态数据归一化"""

    def __init__(self,
                 img_shape=(224, 224),
                 norm_by_imgshape=True):
        self.img_shape = img_shape
        self.norm_by_imgshape = norm_by_imgshape

    def __call__(self, results):
        """归一化姿态坐标"""
        keypoint = results['keypoint']  # (T, V, 3)

        if self.norm_by_imgshape:
            # 按图像尺寸归一化
            keypoint[..., 0] /= self.img_shape[1]  # x坐标
            keypoint[..., 1] /= self.img_shape[0]  # y坐标
            # 置信度保持不变
        else:
            # 按数据本身的范围归一化
            x_coords = keypoint[..., 0]
            y_coords = keypoint[..., 1]

            x_min, x_max = x_coords.min(), x_coords.max()
            y_min, y_max = y_coords.min(), y_coords.max()

            if x_max > x_min:
                keypoint[..., 0] = (x_coords - x_min) / (x_max - x_min)
            if y_max > y_min:
                keypoint[..., 1] = (y_coords - y_min) / (y_max - y_min)

        results['keypoint'] = keypoint
        return results


@DATASETS.register_module()
class PoseSequenceSample:
    """姿态序列采样"""

    def __init__(self,
                 clip_len=40,
                 sampling_strategy='uniform'):
        self.clip_len = clip_len
        self.sampling_strategy = sampling_strategy

    def __call__(self, results):
        """采样姿态序列"""
        keypoint = results['keypoint']  # (T, V, 3)
        total_frames = keypoint.shape[0]

        if total_frames >= self.clip_len:
            if self.sampling_strategy == 'uniform':
                # 均匀采样
                indices = np.linspace(0, total_frames - 1, self.clip_len, dtype=int)
            elif self.sampling_strategy == 'random':
                # 随机连续采样
                start_idx = np.random.randint(0, total_frames - self.clip_len + 1)
                indices = np.arange(start_idx, start_idx + self.clip_len)
            else:
                raise ValueError(f"Unknown sampling strategy: {self.sampling_strategy}")

            sampled_keypoint = keypoint[indices]
        else:
            # 重复填充
            repeat_times = self.clip_len // total_frames + 1
            repeated_keypoint = np.tile(keypoint, (repeat_times, 1, 1))
            sampled_keypoint = repeated_keypoint[:self.clip_len]

        results['keypoint'] = sampled_keypoint
        return results