# -*-coding:utf-8-*-
import torch
from mmaction.registry import DATASETS


@DATASETS.register_module()
class LoadMultiModalData:
    """加载多模态数据的预处理步骤"""

    def __init__(self, pose_format='npy', img_format='jpg'):
        self.pose_format = pose_format
        self.img_format = img_format

    def __call__(self, results):
        """
        加载姿态点数据和图像数据
        """
        import numpy as np
        from PIL import Image

        # 加载姿态点数据 (40, 17, 3)
        pose_file = results['pose_file']
        pose_data = np.load(pose_file)  # 假设是numpy格式
        results['keypoint'] = pose_data

        # 加载图像数据
        img_file = results['img_file']
        img = Image.open(img_file).convert('RGB')
        results['imgs'] = [np.array(img)]

        return results


@DATASETS.register_module()
class FormatMultiModalData:
    """格式化多模态数据"""

    def __init__(self):
        pass

    def __call__(self, results):
        """格式化数据为模型输入格式"""
        # 姿态点数据格式化
        keypoint = results['keypoint']  # (40, 17, 3)
        keypoint = torch.FloatTensor(keypoint)

        # 图像数据格式化
        imgs = results['imgs'][0]  # (224, 224, 3)
        imgs = torch.FloatTensor(imgs).permute(2, 0, 1)  # (3, 224, 224)

        # 标签格式化
        label = results['label']
        label = torch.LongTensor([label])

        results['keypoint'] = keypoint
        results['imgs'] = imgs
        results['label'] = label

        return results