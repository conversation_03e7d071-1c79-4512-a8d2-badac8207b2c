import torch
import numpy as np
from typing import Dict, List, Optional, Tuple
from collections import deque
from mmengine.hooks import Hook
from mmengine.runner import Runner
from mmaction.registry import HOOKS
from mmaction.utils.class_weight_utils import calculate_sample_difficulty


@HOOKS.register_module()
class HardSampleHook(Hook):
    """
    困难样本挖掘Hook，动态识别和重点训练困难样本
    
    Args:
        update_interval: 困难样本更新间隔（epoch）
        difficulty_threshold: 困难样本置信度阈值
        hard_sample_weight: 困难样本权重倍数
        queue_size: 困难样本队列大小
        min_hard_samples: 最小困难样本数量
    """
    
    def __init__(self,
                 update_interval: int = 5,
                 difficulty_threshold: float = 0.7,
                 hard_sample_weight: float = 2.0,
                 queue_size: int = 1000,
                 min_hard_samples: int = 50):
        super().__init__()
        self.update_interval = update_interval
        self.difficulty_threshold = difficulty_threshold
        self.hard_sample_weight = hard_sample_weight
        self.queue_size = queue_size
        self.min_hard_samples = min_hard_samples
        
        # 困难样本队列
        self.hard_sample_queue = deque(maxlen=queue_size)
        self.hard_sample_weights = {}
        
        # 统计信息
        self.total_samples_seen = 0
        self.hard_samples_count = 0
        self.class_difficulty_stats = {0: [], 1: [], 2: []}  # 各类别难度统计
    
    def before_train(self, runner: Runner) -> None:
        """训练开始前初始化"""
        runner.logger.info(f"HardSampleHook initialized with threshold={self.difficulty_threshold}")
        runner.logger.info(f"Hard sample weight multiplier: {self.hard_sample_weight}")
    
    def after_train_iter(self, runner: Runner, batch_idx: int, data_batch: Dict, outputs: Dict) -> None:
        """训练迭代后收集困难样本信息"""
        if not hasattr(outputs, 'loss_cls') or 'cls_score' not in outputs:
            return
        
        # 获取预测和标签
        predictions = outputs['cls_score']  # shape: (N, C)
        if 'gt_label' in data_batch:
            targets = data_batch['gt_label']
        else:
            return
        
        # 计算样本难度
        difficulty_scores, hard_sample_indices = calculate_sample_difficulty(
            predictions, targets, self.difficulty_threshold
        )
        
        # 更新统计信息
        batch_size = predictions.size(0)
        self.total_samples_seen += batch_size
        self.hard_samples_count += len(hard_sample_indices)
        
        # 记录各类别的难度分布
        for i, (score, label) in enumerate(zip(difficulty_scores, targets)):
            class_id = label.item()
            self.class_difficulty_stats[class_id].append(score.item())
            
            # 限制统计队列长度
            if len(self.class_difficulty_stats[class_id]) > 1000:
                self.class_difficulty_stats[class_id] = self.class_difficulty_stats[class_id][-500:]
        
        # 添加困难样本到队列
        for idx in hard_sample_indices:
            sample_info = {
                'difficulty_score': difficulty_scores[idx].item(),
                'class_id': targets[idx].item(),
                'epoch': runner.epoch,
                'iter': runner.iter
            }
            self.hard_sample_queue.append(sample_info)
    
    def after_val_epoch(self, runner: Runner, metrics: Dict[str, float]) -> None:
        """验证后更新困难样本策略"""
        current_epoch = runner.epoch
        
        if (current_epoch + 1) % self.update_interval != 0:
            return
        
        # 分析困难样本分布
        self._analyze_hard_samples(runner)
        
        # 更新困难样本权重策略
        self._update_hard_sample_strategy(runner, metrics)
        
        # 记录统计信息
        self._log_statistics(runner)
    
    def _analyze_hard_samples(self, runner: Runner) -> None:
        """分析困难样本分布"""
        if len(self.hard_sample_queue) < self.min_hard_samples:
            return
        
        # 统计各类别困难样本比例
        class_hard_counts = {0: 0, 1: 0, 2: 0}
        total_hard_samples = len(self.hard_sample_queue)
        
        for sample in self.hard_sample_queue:
            class_id = sample['class_id']
            class_hard_counts[class_id] += 1
        
        # 计算困难样本比例
        hard_sample_ratios = {}
        for class_id in [0, 1, 2]:
            ratio = class_hard_counts[class_id] / total_hard_samples
            hard_sample_ratios[class_id] = ratio
        
        # 存储分析结果
        self.hard_sample_analysis = {
            'total_hard_samples': total_hard_samples,
            'class_hard_counts': class_hard_counts,
            'hard_sample_ratios': hard_sample_ratios,
            'overall_hard_ratio': self.hard_samples_count / max(self.total_samples_seen, 1)
        }
    
    def _update_hard_sample_strategy(self, runner: Runner, metrics: Dict[str, float]) -> None:
        """更新困难样本处理策略"""
        if not hasattr(self, 'hard_sample_analysis'):
            return
        
        analysis = self.hard_sample_analysis
        
        # 根据验证集表现调整困难样本权重
        if 'abnormal_detection_rate' in metrics:
            detection_rate = metrics['abnormal_detection_rate']
            target_rate = 0.97
            
            if detection_rate < target_rate:
                # 检出率低，增加困难样本权重
                self.hard_sample_weight = min(3.0, self.hard_sample_weight * 1.1)
            elif detection_rate > target_rate + 0.02:
                # 检出率过高，可能过拟合，降低困难样本权重
                self.hard_sample_weight = max(1.5, self.hard_sample_weight * 0.95)
        
        # 根据困难样本比例调整阈值
        overall_hard_ratio = analysis['overall_hard_ratio']
        if overall_hard_ratio > 0.3:  # 困难样本过多
            self.difficulty_threshold = max(0.6, self.difficulty_threshold - 0.05)
        elif overall_hard_ratio < 0.1:  # 困难样本过少
            self.difficulty_threshold = min(0.8, self.difficulty_threshold + 0.05)
    
    def _log_statistics(self, runner: Runner) -> None:
        """记录统计信息"""
        if not hasattr(self, 'hard_sample_analysis'):
            return
        
        analysis = self.hard_sample_analysis
        
        runner.logger.info(f"Hard Sample Analysis - Epoch {runner.epoch + 1}:")
        runner.logger.info(f"  Total hard samples: {analysis['total_hard_samples']}")
        runner.logger.info(f"  Overall hard ratio: {analysis['overall_hard_ratio']:.4f}")
        runner.logger.info(f"  Class hard counts: {analysis['class_hard_counts']}")
        runner.logger.info(f"  Hard sample ratios: {analysis['hard_sample_ratios']}")
        runner.logger.info(f"  Current threshold: {self.difficulty_threshold:.3f}")
        runner.logger.info(f"  Current hard weight: {self.hard_sample_weight:.3f}")
        
        # 记录到日志缓冲区
        runner.log_buffer.output['hard_sample_count'] = analysis['total_hard_samples']
        runner.log_buffer.output['hard_sample_ratio'] = analysis['overall_hard_ratio']
        runner.log_buffer.output['difficulty_threshold'] = self.difficulty_threshold
        runner.log_buffer.output['hard_sample_weight'] = self.hard_sample_weight
        
        # 记录各类别难度统计
        for class_id in [0, 1, 2]:
            if self.class_difficulty_stats[class_id]:
                avg_difficulty = np.mean(self.class_difficulty_stats[class_id])
                runner.log_buffer.output[f'class_{class_id}_avg_difficulty'] = avg_difficulty
    
    def get_hard_sample_weights(self, sample_indices: List[int]) -> torch.Tensor:
        """获取样本权重，困难样本获得更高权重"""
        weights = torch.ones(len(sample_indices), dtype=torch.float32)
        
        # 这里需要根据实际的样本索引匹配困难样本
        # 在实际实现中，需要维护样本索引到困难程度的映射
        for i, idx in enumerate(sample_indices):
            if idx in self.hard_sample_weights:
                weights[i] = self.hard_sample_weight
        
        return weights
    
    def state_dict(self) -> Dict:
        """保存Hook状态"""
        return {
            'difficulty_threshold': self.difficulty_threshold,
            'hard_sample_weight': self.hard_sample_weight,
            'total_samples_seen': self.total_samples_seen,
            'hard_samples_count': self.hard_samples_count,
            'hard_sample_queue': list(self.hard_sample_queue)
        }
    
    def load_state_dict(self, state_dict: Dict) -> None:
        """加载Hook状态"""
        self.difficulty_threshold = state_dict.get('difficulty_threshold', self.difficulty_threshold)
        self.hard_sample_weight = state_dict.get('hard_sample_weight', self.hard_sample_weight)
        self.total_samples_seen = state_dict.get('total_samples_seen', 0)
        self.hard_samples_count = state_dict.get('hard_samples_count', 0)
        
        if 'hard_sample_queue' in state_dict:
            self.hard_sample_queue = deque(state_dict['hard_sample_queue'], maxlen=self.queue_size)
