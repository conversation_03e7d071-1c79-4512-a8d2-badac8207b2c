import numpy as np
from typing import Dict, List, Optional, Union
import torch
from mmengine.hooks import Hook
from mmengine.runner import Runner
from mmaction.registry import HOOKS
from mmaction.utils.class_weight_utils import (
    calculate_dynamic_class_weights,
    calculate_class_weights_from_confusion_matrix,
    update_class_weights_with_validation_metrics
)


@HOOKS.register_module()
class ClassWeightHook(Hook):
    """
    动态调整类别权重的Hook
    
    Args:
        update_interval: 权重更新间隔（epoch）
        target_ratios: 目标权重比例 [normal, fake, head_err]
        min_weight: 最小权重限制
        max_weight: 最大权重限制
        target_metrics: 目标指标字典
        adjustment_factor: 权重调整因子
    """
    
    def __init__(self,
                 update_interval: int = 10,
                 target_ratios: Optional[List[float]] = None,
                 min_weight: float = 0.5,
                 max_weight: float = 3.0,
                 target_metrics: Optional[Dict[str, float]] = None,
                 adjustment_factor: float = 0.1):
        super().__init__()
        self.update_interval = update_interval
        self.target_ratios = target_ratios or [1.0, 1.2, 1.1]
        self.min_weight = min_weight
        self.max_weight = max_weight
        self.adjustment_factor = adjustment_factor
        
        # 默认目标指标
        self.target_metrics = target_metrics or {
            'abnormal_detection_rate': 0.97,  # 异常检出率目标
            'normal_false_positive_rate': 0.015  # 正常误判率目标
        }
        
        self.initial_class_counts = None
        self.current_weights = None
    
    def before_train(self, runner: Runner) -> None:
        """训练开始前初始化类别权重"""
        # 获取数据集的类别分布
        train_dataloader = runner.train_dataloader

        runner.logger.info("Initializing class weights...")
        runner.logger.info(f"Train dataloader type: {type(train_dataloader)}")
        runner.logger.info(f"Dataset type: {type(train_dataloader.dataset)}")

        # 调试信息：输出数据集的基本信息
        dataset = train_dataloader.dataset
        runner.logger.info(f"Dataset instance id: {id(dataset)}")
        runner.logger.info(f"Dataset ann_file: {getattr(dataset, 'ann_file', 'NOT_SET')}")
        runner.logger.info(f"Dataset split: {getattr(dataset, 'split', 'NOT_SET')}")
        runner.logger.info(f"Dataset attributes: {[attr for attr in dir(dataset) if not attr.startswith('_')]}")

        if hasattr(dataset, 'data_list'):
            runner.logger.info(f"Dataset data_list length: {len(getattr(dataset, 'data_list', []))}")
            if hasattr(dataset, 'data_list') and len(dataset.data_list) > 0:
                runner.logger.info(f"First sample keys: {list(dataset.data_list[0].keys())}")
        else:
            runner.logger.warning("Dataset does not have data_list attribute")

        if hasattr(train_dataloader.dataset, 'get_class_counts'):
            runner.logger.info("Dataset has get_class_counts method, calling it...")
            try:
                # 如果 data_list 为空，尝试强制重新加载
                if not hasattr(dataset, 'data_list') or not dataset.data_list:
                    runner.logger.warning("Dataset data_list is empty, attempting to reload...")
                    if hasattr(dataset, 'load_data_list'):
                        try:
                            dataset.data_list = dataset.load_data_list()
                            runner.logger.info(f"Reloaded data_list with {len(dataset.data_list)} samples")
                        except Exception as reload_e:
                            runner.logger.error(f"Failed to reload data_list: {reload_e}")

                self.initial_class_counts = train_dataloader.dataset.get_class_counts()
                runner.logger.info(f"Successfully got class counts: {self.initial_class_counts}")

                # 检查类别计数是否有效
                if not self.initial_class_counts or sum(self.initial_class_counts) == 0:
                    runner.logger.warning("Class counts are empty or all zeros, using default values")
                    self.initial_class_counts = getattr(runner.cfg, 'class_counts', [6258, 5494, 4751])

            except Exception as e:
                runner.logger.error(f"Error getting class counts: {e}")
                import traceback
                runner.logger.error(f"Full traceback: {traceback.format_exc()}")
                runner.logger.info("Using default class counts from config")
                self.initial_class_counts = getattr(runner.cfg, 'class_counts', [6258, 5494, 4751])
        else:
            runner.logger.warning("Dataset doesn't have get_class_counts method")
            # 如果数据集没有提供类别统计，使用默认值或从配置获取
            self.initial_class_counts = getattr(runner.cfg, 'class_counts', [6258, 5494, 4751])

        runner.logger.info(f"Final class counts used: {self.initial_class_counts}")

        # 计算初始权重
        self.current_weights = calculate_dynamic_class_weights(
            self.initial_class_counts,
            self.target_ratios,
            self.min_weight,
            self.max_weight
        )

        # 更新模型中的损失函数权重
        self._update_model_weights(runner)

        runner.logger.info(f"Initial class counts: {self.initial_class_counts}")
        runner.logger.info(f"Initial class weights: {self.current_weights}")
    
    def after_val_epoch(self, runner: Runner, metrics: Dict[str, float]) -> None:
        """验证后更新权重"""
        current_epoch = runner.epoch
        
        # 检查是否需要更新权重
        if (current_epoch + 1) % self.update_interval != 0:
            return
        
        # 基于验证指标更新权重
        if self._should_update_weights(metrics):
            new_weights = self._calculate_new_weights(runner, metrics)
            
            if new_weights != self.current_weights:
                old_weights = self.current_weights.copy()
                self.current_weights = new_weights
                
                # 更新模型权重
                self._update_model_weights(runner)
                
                runner.logger.info(
                    f"Epoch {current_epoch + 1}: Updated class weights from "
                    f"{old_weights} to {self.current_weights}"
                )
                
                # 记录权重变化到日志
                for i, (old_w, new_w) in enumerate(zip(old_weights, new_weights)):
                    runner.log_buffer.output[f'class_weight_{i}'] = new_w
                    runner.log_buffer.output[f'weight_change_{i}'] = new_w - old_w
    
    def _should_update_weights(self, metrics: Dict[str, float]) -> bool:
        """判断是否需要更新权重"""
        # 检查关键指标是否偏离目标
        if 'abnormal_detection_rate' in metrics:
            current_rate = metrics['abnormal_detection_rate']
            target_rate = self.target_metrics['abnormal_detection_rate']
            if abs(current_rate - target_rate) > 0.02:  # 偏差超过2%
                return True
        
        if 'normal_false_positive_rate' in metrics:
            current_rate = metrics['normal_false_positive_rate']
            target_rate = self.target_metrics['normal_false_positive_rate']
            if abs(current_rate - target_rate) > 0.005:  # 偏差超过0.5%
                return True
        
        return False
    
    def _calculate_new_weights(self, runner: Runner, metrics: Dict[str, float]) -> List[float]:
        """计算新的类别权重"""
        # 方法1: 基于验证指标更新
        new_weights = update_class_weights_with_validation_metrics(
            self.current_weights,
            metrics,
            self.target_metrics,
            learning_rate=self.adjustment_factor
        )
        
        # 方法2: 如果有混淆矩阵，使用混淆矩阵更新
        if hasattr(runner, 'confusion_matrix') and runner.confusion_matrix is not None:
            new_weights = calculate_class_weights_from_confusion_matrix(
                runner.confusion_matrix,
                self.target_metrics,
                new_weights,
                self.adjustment_factor
            )
        
        return new_weights
    
    def _update_model_weights(self, runner: Runner) -> None:
        """更新模型中的损失函数权重"""
        model = runner.model
        
        # 查找损失函数并更新权重
        if hasattr(model, 'module'):
            model = model.module
        
        # 更新分类头中的损失函数权重
        if hasattr(model, 'cls_head') and hasattr(model.cls_head, 'loss_cls'):
            loss_fn = model.cls_head.loss_cls
            
            # 更新Focal Loss的alpha权重
            if hasattr(loss_fn, 'alpha') and isinstance(loss_fn.alpha, torch.Tensor):
                device = loss_fn.alpha.device
                loss_fn.alpha = torch.tensor(self.current_weights, dtype=torch.float32, device=device)
            
            # 更新标准损失函数的class_weight
            elif hasattr(loss_fn, 'class_weight'):
                if loss_fn.class_weight is not None:
                    device = loss_fn.class_weight.device
                    loss_fn.class_weight = torch.tensor(self.current_weights, dtype=torch.float32, device=device)
    
    def state_dict(self) -> Dict:
        """保存Hook状态"""
        return {
            'current_weights': self.current_weights,
            'initial_class_counts': self.initial_class_counts
        }
    
    def load_state_dict(self, state_dict: Dict) -> None:
        """加载Hook状态"""
        self.current_weights = state_dict.get('current_weights', self.current_weights)
        self.initial_class_counts = state_dict.get('initial_class_counts', self.initial_class_counts)
