import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, List, Union
import numpy as np

from mmaction.registry import MODELS


def calculate_focal_alpha(class_counts: List[int], target_ratios: Optional[List[float]] = None) -> List[float]:
    """
    动态计算Focal Loss的alpha权重
    
    Args:
        class_counts: 各类别样本数量 [normal_count, fake_count, head_err_count]
        target_ratios: 目标权重比例，默认[1.0, 1.2, 1.1]
    
    Returns:
        List[float]: 归一化后的alpha权重
    """
    if target_ratios is None:
        target_ratios = [1.0, 1.2, 1.1]  # 对作弊类别给予更高权重
    
    total_samples = sum(class_counts)
    num_classes = len(class_counts)
    
    # 计算基础权重（与样本数成反比）
    alpha_weights = []
    for count, ratio in zip(class_counts, target_ratios):
        base_weight = total_samples / (num_classes * count)
        adjusted_weight = base_weight * ratio
        alpha_weights.append(adjusted_weight)
    
    # 归一化权重
    alpha_sum = sum(alpha_weights)
    alpha_weights = [w / alpha_sum * num_classes for w in alpha_weights]
    
    return alpha_weights


@MODELS.register_module()
class FocalLoss(nn.Module):
    """
    Focal Loss实现，用于处理类别不平衡和困难样本
    
    Args:
        alpha: 类别权重，可以是float、list或'auto'
        gamma: 聚焦参数，控制难易样本的权重差异
        reduction: 损失聚合方式 ('mean', 'sum', 'none')
        class_counts: 各类别样本数量，用于自动计算alpha
    """
    
    def __init__(self,
                 alpha: Union[float, List[float], str] = 1.0,
                 gamma: float = 2.0,
                 reduction: str = 'mean',
                 class_counts: Optional[List[int]] = None):
        super(FocalLoss, self).__init__()
        self.gamma = gamma
        self.reduction = reduction
        
        # 处理alpha参数
        if isinstance(alpha, str) and alpha == 'auto':
            if class_counts is None:
                raise ValueError("class_counts must be provided when alpha='auto'")
            alpha = calculate_focal_alpha(class_counts)
        
        if isinstance(alpha, (list, tuple)):
            self.alpha = torch.tensor(alpha, dtype=torch.float32)
        else:
            self.alpha = alpha
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            inputs: 模型预测logits，shape: (N, C)
            targets: 真实标签，shape: (N,)
        
        Returns:
            torch.Tensor: Focal Loss值
        """
        # 计算交叉熵
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        # 计算预测概率
        pt = torch.exp(-ce_loss)
        
        # 计算alpha权重
        if isinstance(self.alpha, torch.Tensor):
            if self.alpha.device != inputs.device:
                self.alpha = self.alpha.to(inputs.device)
            alpha_t = self.alpha[targets]
        else:
            alpha_t = self.alpha
        
        # 计算Focal Loss
        focal_loss = alpha_t * (1 - pt) ** self.gamma * ce_loss
        
        # 应用reduction
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


@MODELS.register_module()
class FocalLossWithSmoothing(nn.Module):
    """
    结合Label Smoothing的Focal Loss

    Args:
        alpha: 类别权重，可以是float、list或'auto'
        gamma: 聚焦参数，控制难易样本的权重差异
        label_smoothing: 标签平滑参数，范围[0, 1)
        reduction: 损失聚合方式 ('mean', 'sum', 'none')
        class_counts: 各类别样本数量，用于自动计算alpha
    """

    def __init__(self,
                 alpha: Union[float, List[float], str] = 1.0,
                 gamma: float = 2.0,
                 label_smoothing: float = 0.1,
                 reduction: str = 'mean',
                 class_counts: Optional[List[int]] = None):
        super(FocalLossWithSmoothing, self).__init__()
        self.gamma = gamma
        self.label_smoothing = label_smoothing
        self.reduction = reduction

        # 处理alpha参数
        if isinstance(alpha, str) and alpha == 'auto':
            if class_counts is None:
                raise ValueError("class_counts must be provided when alpha='auto'")
            alpha = calculate_focal_alpha(class_counts)

        if isinstance(alpha, (list, tuple)):
            self.alpha = torch.tensor(alpha, dtype=torch.float32)
        else:
            self.alpha = alpha

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            inputs: 模型预测logits，shape: (N, C)
            targets: 真实标签，shape: (N,)

        Returns:
            torch.Tensor: Focal Loss with Label Smoothing值
        """
        num_classes = inputs.size(1)

        # 应用Label Smoothing
        if self.label_smoothing > 0:
            # 创建平滑标签
            smooth_targets = torch.zeros_like(inputs)
            smooth_targets.fill_(self.label_smoothing / (num_classes - 1))
            smooth_targets.scatter_(1, targets.unsqueeze(1), 1.0 - self.label_smoothing)

            # 计算KL散度损失
            log_probs = F.log_softmax(inputs, dim=1)
            kl_loss = -smooth_targets * log_probs
            kl_loss = kl_loss.sum(dim=1)

            # 计算预测概率用于focal权重
            probs = F.softmax(inputs, dim=1)
            pt = probs.gather(1, targets.unsqueeze(1)).squeeze(1)
        else:
            # 标准交叉熵
            kl_loss = F.cross_entropy(inputs, targets, reduction='none')
            pt = torch.exp(-kl_loss)

        # 计算alpha权重
        if isinstance(self.alpha, torch.Tensor):
            if self.alpha.device != inputs.device:
                self.alpha = self.alpha.to(inputs.device)
            alpha_t = self.alpha[targets]
        else:
            alpha_t = self.alpha

        # 计算Focal Loss
        focal_loss = alpha_t * (1 - pt) ** self.gamma * kl_loss

        # 应用reduction
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss
