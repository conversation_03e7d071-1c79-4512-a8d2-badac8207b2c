# Copyright (c) OpenMMLab. All rights reserved.
from .base import BaseH<PERSON>
from .feature_head import FeatureHead
from .gcn_head import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .i3d_head import I3DHead
from .mvit_head import MViTHead
from .omni_head import OmniHead
from .rgbpose_head import RGB<PERSON><PERSON><PERSON><PERSON>
from .slowfast_head import SlowFastHead
from .timesformer_head import TimeS<PERSON>Head
from .tpn_head import TP<PERSON><PERSON><PERSON>
from .trn_head import TRNHead
from .tsm_head import TSMHead
from .tsn_audio_head import TSNAudioHead
from .tsn_head import TSNHead
from .uniformer_head import UniFormerHead
from .x3d_head import X3DHead

from .twofusion_head import TwoFusionHead       # add @Moss

__all__ = [
    'BaseHead', 'GCNHead', 'I3DHead', 'MViTHead', 'OmniHead', 'SlowFastHead',
    'TPNHead', 'TRNHead', 'TSMHead', 'TSNAudioHead', 'TSNHead',
    'TimeSformerHead', 'UniFormerHead', 'RGBPoseHead', 'X3DHead', 'FeatureHead',
    'TwoFusionHead'
]
