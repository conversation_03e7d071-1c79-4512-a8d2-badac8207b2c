# -*-coding:utf-8-*-
import torch
import torch.nn as nn
from mmaction.registry import MODELS
from mmaction.models.heads.base import BaseHead


@MODELS.register_module(force=True)
class TwoFusionHead(BaseHead):
    """
    多模态融合特征（图像+骨骼）分类头   created by @Moss: 20250604 17:05
    *Ref claude4 Team
    """
    def __init__(self,
                 num_classes=3,
                 in_channels=512,  # 融合特征维度
                 dropout_ratio=0.5,
                 init_std=0.01,
                 **kwargs):
        super().__init__(num_classes, in_channels, **kwargs)

        self.dropout_ratio = dropout_ratio
        self.init_std = init_std

        if self.dropout_ratio != 0:
            self.dropout = nn.Dropout(p=self.dropout_ratio)
        else:
            self.dropout = None

        # 多层MLP用于特征细化
        self.fc_layers = nn.Sequential(
            nn.Linear(in_channels, in_channels // 2),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(in_channels // 2),
            nn.Dropout(p=dropout_ratio) if dropout_ratio > 0 else nn.Identity(),

            nn.Linear(in_channels // 2, in_channels // 4),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(in_channels // 4),
            nn.Dropout(p=dropout_ratio) if dropout_ratio > 0 else nn.Identity()
        )

        # 最终分类层
        self.fc_cls = nn.Linear(in_channels // 4, num_classes)

    def init_weights(self):
        """Initialize the weights."""
        for m in self.fc_layers:
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, self.init_std)
                nn.init.constant_(m.bias, 0)
        nn.init.normal_(self.fc_cls.weight, 0, self.init_std)
        nn.init.constant_(self.fc_cls.bias, 0)

    def forward(self, x, **kwargs):
        """Forward function.

        Args:
            x (torch.Tensor): The input tensor of shape (N, in_channels).

        Returns:
            torch.Tensor: The classification scores of shape (N, num_classes).
        """
        # 确保输入是2D的
        if x.dim() > 2:
            x = x.view(x.size(0), -1)

        # 通过MLP层
        x = self.fc_layers(x)

        # 最终分类
        cls_score = self.fc_cls(x)

        return cls_score




if __name__ == '__main__':
    # 设置随机种子以确保可重复性
    torch.manual_seed(2025)
    batch_size = 2

    cls_head = TwoFusionHead(
        num_classes=3,              # 类别
        in_channels=512,
        loss_cls=dict(
            type='CrossEntropyLoss',
            loss_weight=1.0,
            class_weight=[1.0, 2.0, 1.5]  # 类别权重
        )
    )

    fusionFeature = torch.randn(batch_size, 512)

    output = cls_head.forward(fusionFeature)
    print(output, output.shape)
