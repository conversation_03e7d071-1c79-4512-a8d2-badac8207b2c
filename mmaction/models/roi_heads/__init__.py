# Copyright (c) OpenMMLab. All rights reserved.
try:
    from mmdet.registry import <PERSON><PERSON><PERSON><PERSON> as M<PERSON>ET_MODELS

    from .bbox_heads import BBoxHeadAVA
    from .roi_extractors import SingleRoIExtractor3D
    from .roi_head import <PERSON>VARo<PERSON>Head
    from .shared_heads import <PERSON><PERSON><PERSON><PERSON><PERSON>, FBOHead, LFBInferH<PERSON>

    for module in [
            AVARoIHead, BBoxHeadAVA, SingleRoIExtractor3D, ACRNHead, FBOHead,
            LFBInferHead
    ]:

        MMDET_MODELS.register_module()(module)

    __all__ = [
        'AVARoIHead', 'BBoxHeadAVA', 'SingleRoIExtractor3D', 'ACRNHead',
        'FBOHead', 'LFBInferHead'
    ]

except (ImportError, ModuleNotFoundError):
    pass
