# -*-coding:utf-8-*-
import torch
import torch.nn as nn
from typing import Dict

from mmaction.registry import M<PERSON><PERSON><PERSON>
from mmaction.utils import SampleList
from mmaction.models.recognizers.base import BaseRecognizer
import torch.nn.functional as F




@MODELS.register_module(force=True)               # force=True
class MultiModalRecognizer_BaseR50(BaseRecognizer):
    """
    Multi-modal recognizer for 2D pose sequence and RGB image.
    """

    def __init__(self,
                 pose_backbone: dict,  # Config for your pose backbone
                 image_backbone: dict,  # Config for your image backbone
                 fusion_neck: dict,  # Use a different name to avoid conflict with BaseRecognizer's 'neck'
                 cls_head: dict = None,  # Use a different name for clarity
                 train_cfg: dict = None,
                 test_cfg: dict = None,
                 data_preprocessor: dict = None,
                 **kwargs):  # Catch-all for other BaseModel args if any
        super(BaseRecognizer,
              self).__init__(data_preprocessor=data_preprocessor)

        # 构建特征骨干网络
        self.pose_backbone = MODELS.build(pose_backbone)
        self.image_backbone = MODELS.build(image_backbone)

        self.pose_backbone_config = pose_backbone  # Store for dim inference
        self.image_backbone_config = image_backbone  # Store for dim inference

        # 适配器参数
        self.pose_adapter_out_dim = fusion_neck.get('pose_feat_dim', 512) if fusion_neck else 512
        self.img_adapter_out_dim = fusion_neck.get('img_feat_dim', 512) if fusion_neck else 512

        self.pose_feat_dim = self.pose_backbone_config.get('num_class', 256)  # For STGCN-like
        self.image_feat_dim = 2048  # Default * for ResNet50 stage 4
        if 'type' in self.image_backbone_config and 'ResNet' in self.image_backbone_config['type']:
            if self.image_backbone_config.get('depth') == 50 and \
                    self.image_backbone_config.get('out_indices') == (3,):
                self.image_feat_dim = 2048
            else:
                print(f"Warning: ResNet image_feat_dim for "
                      f"depth={self.image_backbone_config.get('depth')} and "
                      f"out_indices={self.image_backbone_config.get('out_indices')} "
                      f"might not be 2048. Please verify.")
        else:
            print(f"@Moss: image_feat_dim for {self.image_backbone_config.get('type', 'UnknownType')} "
                  f"not explicitly handled for ResNet logic. Assuming {self.image_feat_dim}. Please verify.")

        # 构建适配器网络
        self.pose_adapter = self._build_adapter()
        self.image_adapter = self._build_adapter(is_image=True)

        if fusion_neck is not None:
            self.fusion_neck_model = MODELS.build(fusion_neck)

        # 分类头
        if cls_head is not None:
            self.cls_head = MODELS.build(cls_head)

        # 训练和测试配置
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg


    def _build_adapter(self, is_image: bool = False) -> nn.Module:
        if is_image:
            return nn.Sequential(
                nn.AdaptiveAvgPool2d(1), nn.Flatten(),      # 空间维度池化
                nn.Linear(self.image_feat_dim, self.img_adapter_out_dim), nn.ReLU(), nn.Dropout(0.5))
        else:  # Pose
            return nn.Sequential(
                nn.AdaptiveAvgPool2d(1), nn.Flatten(),  # 时序维度池化
                nn.Linear(self.pose_feat_dim, self.pose_adapter_out_dim), nn.ReLU(), nn.Dropout(0.5))


    def init_weights(self) -> None:
        """
        为所有子模块（主干网络、适配器、融合模块、分类头）初始化权重。
        由于本模型是多主干网络结构，我们不调用父类(BaseRecognizer)的init_weights，
        因为它假设了单主干网络的存在。
        """
        # 1. 初始化图像主干网络
        # MMEngine/MMCV 会自动处理带有 init_cfg 的模块。
        # 你的 ResNet 配置了 init_cfg=dict(type='Pretrained', checkpoint='torchvision://resnet50')
        # 所以 MMEngine 的初始化流程会自动加载预训练权重。
        # 我们手动调用其 init_weights() 是一个安全的、明确的做法，它会触发 init_cfg 的逻辑。
        if hasattr(self.image_backbone, 'init_weights'):
            self.image_backbone.init_weights()

        # 2. 初始化姿态主干网络
        # 如果 STGCN 有自己的 init_weights 逻辑或 init_cfg，也这样调用。
        if hasattr(self.pose_backbone, 'init_weights'):
            self.pose_backbone.init_weights()

        # 3. 初始化你自定义的适配器（你已经做得很好了）
        # 这部分代码是正确的，因为这些简单的 nn.Sequential 没有 init_cfg，需要手动初始化。
        print("Initializing custom adapter weights...")
        for m in self.pose_adapter.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        for m in self.image_adapter.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

        # 4. 初始化融合 Neck 和分类头
        # 同样，让它们自己处理自己的初始化（通常通过 init_cfg 或其自身的 init_weights）
        if self.fusion_neck_model and hasattr(self.fusion_neck_model, 'init_weights'):
            self.fusion_neck_model.init_weights()

        if self.cls_head and hasattr(self.cls_head, 'init_weights'):
            self.cls_head.init_weights()


    def preprocess_pose_data(self, keypoint_tensor: torch.Tensor) -> torch.Tensor:
        """
        将2D姿态数据转换为模型期望格式
        输入: (B, 40, 17, 3) - (batch, time, joints, coords)
        输出: 根据backbone类型转换
        """
        # Example for STGCN, replace with actual type check if needed
        if 'STGCN' in str(type(self.pose_backbone)):  # A bit fragile but works if STGCN is in class name
            B, T, V, C_in = keypoint_tensor.shape
            pose_data = keypoint_tensor.unsqueeze(1)  # (B, Member, T, V, C)
            return pose_data

    def preprocess_img_data(self, img_tensor: torch.Tensor) -> torch.Tensor:
        """
        将 多帧图像数据 转换为 模型期望格式
        输入: 多通道图像(batch, MUltiChannel, H, W)
        输出: 根据backbone类型转换
        """
        # (B, C, H, W)
        if len(img_tensor.shape) > 4:
            B, F, C, H, W = img_tensor
            img_tensor = img_tensor.view(B, F*C, H, W)

        return img_tensor


    def forward_once(self, pose_keypoint, imgs):
        """Input: 预处理后的图像和骨骼序列"""

        # 姿态特征及适配特征
        pose_feat_raw = self.pose_backbone(pose_keypoint)                    # pose_data_preprocessed
        if len(pose_feat_raw.shape) == 5:
            B, M, F, T, V =pose_feat_raw.shape
            pose_feat_raw = pose_feat_raw.squeeze(1)
        pose_feat_adapted = self.pose_adapter(pose_feat_raw)      # (B, pose_adapter_dim):(torch.randn(2, 256, 170))


        # 图像特征及适配特征
        img_feat_raw = self.image_backbone(imgs)  # self.backbone  输入处理后的图像 imgs_reshaped
        # print('img_feat_raw shape:', img_feat_raw.shape)
        if isinstance(img_feat_raw, tuple):
            img_feat_raw = img_feat_raw[-1]
        img_feat_final_adapted = self.image_adapter(img_feat_raw)       # (B, img_adapter_out_dim) 多帧图像堆叠在通道

        # neck-Fusion
        if self.fusion_neck_model:
            fused_feat = self.fusion_neck_model(pose_feat_adapted, img_feat_final_adapted)
        else:
            fused_feat = torch.cat([pose_feat_adapted, img_feat_final_adapted], dim=1)

        return fused_feat

    def extract_feat(self, inputs: tuple,
                     test_mode: bool = False) -> tuple:
        """
        提取姿态和图像特征. created by @Moss 20250605 11:45
        `inputs` from data_preprocessor are the (keypoints, images).
        """
        labels = None
        if test_mode:
            keypoint_tensor, imgs_tensor = inputs
        else:
            keypoint_tensor, imgs_tensor, labels = inputs

        # ===Data Preprocess: 2D Pose Sequence, Process RGB Images ===
        pose_data_pred = self.preprocess_pose_data(keypoint_tensor)
        img_data_pred = self.preprocess_img_data(imgs_tensor)

        # 提取融合特征的前向传播
        fused_feat = self.forward_once(pose_data_pred, img_data_pred)

        # 分类头
        if self.cls_head is not None:
            if test_mode:
                cls_score = self.cls_head(fused_feat)
                return cls_score
            else:
                cls_score = self.cls_head(fused_feat)
                losses = self.cls_head.loss(cls_score, labels)
                return cls_score, losses
        else:
            return fused_feat

    def loss(self, inputs: torch.Tensor, data_samples: SampleList, **kwargs) -> Dict[str, torch.Tensor]:
        """
        训练时的损失计算
        Args:
            inputs: 输入数据
            data_samples: 数据样本，包含标签等信息
        Returns:
            损失字典
        """
        # 提取特征
        feats = self.extract_feat(inputs, **kwargs)

        # 计算损失
        if self.cls_head is not None:
            loss_cls = self.cls_head.loss(feats, data_samples, **kwargs)
            return loss_cls
        else:
            return {'loss': torch.tensor(0.0, device=feats.device)}


def speed_test_BaseR50C6():
    """
    将3帧数据叠加到通道，即为R50-Baes Channel
    """
    import time
    from functools import partial
    class mod_timer:
        """
        This is a Decorator of Timer module for func and code part:
        Usage:
        way1. @mod_timer
              def your_func():
                pass
        way2. with mod_timer() as t:
                your codes
        """

        def __init__(self, func=None):

            self.func = func
            self.start_code = None  # 片段
            self.end_code = None  # 片段

        def __call__(self, *args, **kwargs):
            _start = time.time()
            result = self.func(*args, **kwargs)
            _end = time.time()
            print(f"{self.func.__name__} run {_end - _start:.4f} s.")

            return result

        def __get__(self, obj):
            return partial(self.__call__, obj)

        def __enter__(self):
            self.start_code = time.time()
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            self.end_code = time.time()
            print(f"This part run {self.end_code - self.start_code:.4f} s.")

        def __repr__(self):
            return f"<Mod_timer used for {self.func.__name__}>"

    torch.manual_seed(2025)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(2025)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(device)

    # 定义输入特征维度
    batch_size = 1
    # T=num_frames, V=num_keypoints, C_in=dims_per_keypoint (e.g., 3 for x,y,score)
    num_frames_pose = 30
    num_keypoints = 17          # COCO
    pose_dims = 3
    dummy_pose_keypoints = torch.randn(batch_size, num_frames_pose, num_keypoints, pose_dims)
    # For ResNet (2D), input is (B_eff, C_rgb, H, W)
    img_channels = 3
    img_h, img_w = 448, 448
    dummy_images = torch.randn(batch_size, img_channels, img_h, img_w)
    # model param
    pose_backbone=dict(
        type='STGCN',  # 或其他姿态骨干网络 PoseC3D
        in_channels=3,          # xyc
        graph_cfg=dict(
            layout='coco',        # COCO 17个关键点
            mode='stgcn_spatial'),
        # edge_importance_weighting=True,
        # data_bn=True,
        # init_cfg=dict(type='Pretrained', checkpoint='path/to/stgcn_pretrained.pth')       # Add pretrained STGCN weights if available, e.g.
    )
    image_backbone=dict(
        type='ResNet',
        depth=50,
        num_stages=4,
        out_indices=(3,),  # 最后一层特征 (stage 4)
        frozen_stages=-1,       # Unfreeze all stages, or -1 for all, 1 for stem + stage1 etc.
        norm_cfg=dict(type='BN', requires_grad=True),
        norm_eval=True,         # # Set to False for training BN layers
        style='pytorch',
        init_cfg=dict(type='Pretrained', checkpoint='torchvision://resnet50')
    )

    fusion_neck=dict(
        type='MultiModalFusionNeck',        # 适配器
        pose_feat_dim=256,              # ST-GCN 输出维度(num_class=256)
        img_feat_dim=2048,              # ResNet50输出维度(stage 4为 2048)
        fusion_dim=512,
        fusion_type='cross_modal',  # 跨模态融合
        dropout=0.5
    )
    cls_head=dict(
        type='TwoFusionHead',              # 双流融合特征分类头
        num_classes=3,              # 类别
        in_channels=512,
        loss_cls=dict(
            type='CrossEntropyLoss',
            loss_weight=1.0,
            class_weight=[1.0, 2.0, 1.5])
    )

    # 创建随机输入张量
    inputs = (dummy_pose_keypoints.cuda(), dummy_images.cuda())

    model = MultiModalRecognizer_BaseR50(pose_backbone, image_backbone, fusion_neck, cls_head)
    model = model.to(device)

    model.eval()

    # 预热
    starter, ender = torch.cuda.Event(enable_timing=True), torch.cuda.Event(enable_timing=True)
    starter.record()
    for _ in range(50):
        # with mod_timer():
        _ = model.extract_feat(inputs, test_mode=True)  # 调用 forward 方法
    ender.record()

    torch.cuda.synchronize()  # 同步GPU时间
    warm_up_time = starter.elapsed_time(ender)
    print("GPU warm up time:{:.2f}毫秒".format(warm_up_time))  # starter.elapsed_time单位：ms

    for _ in range(50):
        with mod_timer():
            model.extract_feat(inputs, test_mode=True)  # 调用 forward 方法
    # 输出结果
    fused_feat = model.extract_feat(inputs, test_mode=True)  # 调用 forward 方法
    print("output shape:", fused_feat.shape, fused_feat)  # (batch, fusion_dim)


# -----------------------------------------------------------------------------------------------------------------------


class Squeeze(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, x):
        return x.squeeze(self.dim)


class AdaptivePoseAdapter(nn.Module):
    """
    用于处理来自STGCN等姿态骨干网络特征的适配器。

    该模块接收来自姿态骨干网络的4D张量 (N, C, T, V)，
    执行时间池化，展平特征，并将其投影到目标维度。

    Args:
        input_channels (int): 来自姿态骨干网络的通道数 (C)。
        num_keypoints (int): 姿态数据中的关键点/顶点数 (V)。
        output_dim (int): 期望的输出特征维度。
        dropout_p (float): Dropout概率。默认为 0.5。
    """

    def __init__(self, input_channels, num_keypoints, output_dim, dropout_p=0.5):
        super().__init__()
        # 对时间维度 (T) 进行池化
        self.pool = nn.AdaptiveAvgPool2d((1, None))
        # 移除被压缩的时间维度
        self.squeeze = Squeeze(2)
        # 展平 C 和 V 维度
        self.flatten = nn.Flatten()

        # 线性层的输入维度将是 C * V
        self.linear = nn.Linear(input_channels * num_keypoints, output_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_p)

    def forward(self, x):
        # 输入形状: (N, C, T, V)
        x = self.pool(x)  # -> (N, C, 1, V)
        x = self.squeeze(x)  # -> (N, C, V)
        x = self.flatten(x)  # -> (N, C * V)
        x = self.linear(x)
        x = self.relu(x)
        x = self.dropout(x)
        return x


class AttentivePoseAdapter(nn.Module):
    """
    使用 注意力机制 处理来自STGCN等姿态骨干网络特征的适配器。

    该模块接收来自姿态骨干网络的4D张量 (N, C, T, V)，
    学习每个时间步的重要性权重，进行加权池化，
    然后将展平的特征投影到目标维度。

    Args:
        input_channels (int): 来自姿态骨干网络的通道数 (C)。
        num_keypoints (int): 姿态数据中的关键点/顶点数 (V)。
        output_dim (int): 期望的输出特征维度。
        dropout_p (float): Dropout概率。默认为 0.5。
    """

    def __init__(self, input_channels, num_keypoints, output_dim, dropout_p=0.5):
        super().__init__()
        self.input_channels = input_channels
        self.num_keypoints = num_keypoints

        feature_dim_per_step = input_channels * num_keypoints

        # 这个网络用来计算每个时间步的重要性得分
        self.attention_net = nn.Sequential(
            nn.Linear(feature_dim_per_step, 128),  # 可以调整隐藏层维度
            nn.Tanh(),
            nn.Linear(128, 1)
        )

        # 后续的线性投影层
        self.linear = nn.Linear(feature_dim_per_step, output_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout_p)

    def forward(self, x):
        # 输入形状: (N, C, T, V)
        N, C, T, V = x.shape

        # 1. 准备计算注意力的输入， 将每个时间步的特征展平
        x_permuted = x.permute(0, 2, 1, 3).contiguous()  # -> (N, T, C, V)
        x_flat_time = x_permuted.view(N * T, C * V)  # -> (N*T, C*V)

        # 2. 计算注意力权重
        att_scores = self.attention_net(x_flat_time)  # -> (N*T, 1)
        att_scores = att_scores.view(N, T, 1)  # -> (N, T, 1)，每个时间步一个分数

        # 使用 softmax 将分数转换为概率（权重）
        att_weights = F.softmax(att_scores, dim=1)  # -> (N, T, 1)，权重和为1

        # 3. 应用注意力权重进行加权池化
        # (N, T, C*V) * (N, T, 1) -> (N, T, C*V)
        x_reshaped = x_permuted.view(N, T, -1)
        x_weighted = x_reshaped * att_weights

        # 对加权后的特征求和，得到最终的聚合特征
        x_att_pooled = x_weighted.sum(dim=1)  # -> (N, C*V)

        # 4. 通过最终的投影层
        x_out = self.linear(x_att_pooled)
        x_out = self.relu(x_out)
        x_out = self.dropout(x_out)

        return x_out


class AdaptiveImageAdapter(nn.Module):
    """
    一个简化的图像适配器，接收已经过池化和展平的2D特征张量 (B, C)，
    并将其投影到目标维度。
    """

    def __init__(self, input_dim, output_dim):
        super().__init__()
        # 移除了 pool 和 flatten，因为它们在模型主流程中处理
        self.linear = nn.Linear(input_dim, output_dim)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.5)

    def forward(self, x):
        # 输入形状期望是 (B, C)
        if len(x.shape) != 2:
            raise ValueError(f"AdaptiveImageAdapter expects a 2D tensor (B, C), but got shape {x.shape}")

        x = self.linear(x)
        x = self.relu(x)
        x = self.dropout(x)
        return x



@MODELS.register_module()               # force=True
class MultiModalRecognizer(BaseRecognizer):
    """
    @Moss: Multi-modal recognizer for 2D pose sequence and RGB image.
    """

    def __init__(self,
                 pose_backbone: dict,
                 image_backbone: dict,
                 fusion_neck: dict,
                 cls_head: dict = None,
                 train_cfg: dict = None,
                 test_cfg: dict = None,
                 data_preprocessor: dict = None,
                 **kwargs):
        super(BaseRecognizer, self).__init__(data_preprocessor=data_preprocessor)

        # 构建特征骨干网络
        self.pose_backbone = MODELS.build(pose_backbone)
        self.image_backbone = MODELS.build(image_backbone)

        self.pose_backbone_config = pose_backbone
        self.image_backbone_config = image_backbone

        # 适配器参数
        self.pose_adapter_out_dim = fusion_neck.get('pose_feat_dim', 512) if fusion_neck else 512
        self.img_adapter_out_dim = fusion_neck.get('img_feat_dim', 512) if fusion_neck else 512

        self.pose_feat_dim = self.pose_backbone_config.get('num_class', 256)

        # 修改图像特征维度计算逻辑以适配 MobileNetV2TSM
        # self.image_feat_dim = 1280  # 默认为 MobileNetV2 最后一层特征维度
        backbone_type = self.image_backbone_config.get('type', 'UnknownType')
        # 根据骨干网络配置动态确定特征维度
        if 'MobileNetV2TSM' in backbone_type:
            # out_indices=(6,) 对应 MobileNetV2 的 stage 6, 其输出通道为 320
            if self.image_backbone_config.get('out_indices') == (6,):
                self.image_feat_dim = 320
                print(f"@Moss: Detected MobileNetV2TSM with out_indices=(6,), setting image_feat_dim={self.image_feat_dim}")
            else:
                # 默认 MobileNetV2TSM/MobileNetV2 最终输出为 1280
                self.image_feat_dim = 1280
                print(f"@Moss: Using {backbone_type} as backbone, assuming final layer output, image_feat_dim={self.image_feat_dim}")
        else:
            # 为其他骨干网络设置一个默认值或报错
            self.image_feat_dim = 1280  # 或者其他合理的默认值
            print(f"@Moss: image_feat_dim for {backbone_type} "
                  f"assumed to be {self.image_feat_dim}. Please verify.")


        # 构建适配器网络
        self.pose_adapter = self._build_adapter()
        self.image_adapter = self._build_adapter(is_image=True)

        if fusion_neck is not None:
            self.fusion_neck_model = MODELS.build(fusion_neck)

        # 分类头
        if cls_head is not None:
            self.cls_head = MODELS.build(cls_head)

        # 训练和测试配置
        self.train_cfg = train_cfg
        self.test_cfg = test_cfg


    def _build_adapter(self, is_image: bool = False) -> nn.Module:
        if is_image:
            return AdaptiveImageAdapter(self.image_feat_dim, self.img_adapter_out_dim)
        else:  # Pose
            # 注意：此适配器的输入应为 (N, C, T, V)   这是在 forward 传递中挤压掉 'M' 维度之后。
            # return AdaptivePoseAdapter(input_channels=self.pose_feat_dim, num_keypoints=17, output_dim=self.pose_adapter_out_dim)
            # 注意力适配器
            return AttentivePoseAdapter(input_channels=self.pose_feat_dim, num_keypoints=17, output_dim=self.pose_adapter_out_dim)



    def init_weights(self) -> None:
        """
        为所有子模块（主干网络、适配器、融合模块、分类头）初始化权重。
        由于本模型是多主干网络结构，我们不调用父类(BaseRecognizer)的init_weights，
        因为它假设了单主干网络的存在。
        """
        # 1. 初始化图像主干网络
        # MMEngine/MMCV 会自动处理带有 init_cfg 的模块。
        # 你的 ResNet 配置了 init_cfg=dict(type='Pretrained', checkpoint='torchvision://resnet50')
        # 所以 MMEngine 的初始化流程会自动加载预训练权重。
        # 我们手动调用其 init_weights() 是一个安全的、明确的做法，它会触发 init_cfg 的逻辑。
        if hasattr(self.image_backbone, 'init_weights'):
            self.image_backbone.init_weights()

        # 2. 初始化姿态主干网络
        # 如果 STGCN 有自己的 init_weights 逻辑或 init_cfg，也这样调用。
        if hasattr(self.pose_backbone, 'init_weights'):
            self.pose_backbone.init_weights()

        # 3. 初始化你自定义的适配器（你已经做得很好了）
        # 这部分代码是正确的，因为这些简单的 nn.Sequential 没有 init_cfg，需要手动初始化。
        print("Initializing custom adapter weights...")
        for m in self.pose_adapter.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        for m in self.image_adapter.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

        # 4. 初始化融合 Neck 和分类头
        # 同样，让它们自己处理自己的初始化（通常通过 init_cfg 或其自身的 init_weights）
        if self.fusion_neck_model and hasattr(self.fusion_neck_model, 'init_weights'):
            self.fusion_neck_model.init_weights()

        if self.cls_head and hasattr(self.cls_head, 'init_weights'):
            self.cls_head.init_weights()


    def preprocess_pose_data(self, keypoint_tensor: torch.Tensor) -> torch.Tensor:
        """
        将2D姿态数据转换为新版STGCN期望格式 单人
        输入: (B, T, V, C_in) - (batch, time, joints, coords)
        输出: (N, M, T, V, C) - (batch, person, time, joints, coords)
        """
        if len(keypoint_tensor.shape) == 4:
            # (B, T, V, C_in) -> (B, 1, T, V, C_in)
            pose_data = keypoint_tensor.unsqueeze(1)
        else:
            raise ValueError(f"@Moss: keypoint_tensor dim is not 4")
        return pose_data

    def preprocess_img_data(self, img_tensor: torch.Tensor) -> torch.Tensor:
        """
        将图像数据转换为TSM模型期望格式
        TSM期望输入: (B*T, C, H, W)，其中T是num_segments
        """
        if len(img_tensor.shape) == 5:
            # (B, T, C, H, W) -> (B*T, C, H, W)
            B, T, C, H, W = img_tensor.shape          # .permute(0, 2, 1, 3, 4)
            img_tensor = img_tensor.contiguous()  # (B, T, C, H, W)
            img_tensor = img_tensor.view(B * T, C, H, W)
        elif len(img_tensor.shape) == 4:
            B, C, H, W = img_tensor.shape
            # 对于单帧输入，需要复制为num_segments帧
            if hasattr(self.image_backbone, 'num_segments'):
                T = self.image_backbone.num_segments
                # 重复帧以满足TSM的要求
                img_tensor = img_tensor.unsqueeze(1).repeat(1, T, 1, 1, 1)  # (B, T, C, H, W)
                img_tensor = img_tensor.view(B * T, C, H, W)  # (B*T, C, H, W)
            else:
                # 如果没有num_segments，假设T=1
                pass  # 保持 (B, C, H, W) 格式
        else:
            raise ValueError(f"Unsupported image tensor shape: {img_tensor.shape}")

        return img_tensor

    def forward_once(self, pose_keypoint, imgs):
        """Input: 预处理后的图像和骨骼序列"""

        # 姿态特征及适配特征
        pose_feat_raw = self.pose_backbone(pose_keypoint)       # （1, 1, frame=35, 17, 3）-> (1, 1, 256, Sampled-Time=ceil(Frame/2/2), 17)
        if len(pose_feat_raw.shape) == 5:       # B, M, F, T, V = pose_feat_raw.shape
            pose_feat_raw = pose_feat_raw.squeeze(1)        # (1, 256, 9, 17)
        pose_feat_adapted = self.pose_adapter(pose_feat_raw)    # (1, 256)

        # TSM图像特征提取
        img_feat_raw = self.image_backbone(imgs)

        # TSM输出是 (B*T, feat_dim, H, W)，需要重新整理为 (B, feat_dim)
        if hasattr(self.image_backbone, 'num_segments'):
            BT, C, H, W = img_feat_raw.shape
            B = BT // self.image_backbone.num_segments
            T = self.image_backbone.num_segments

            # 重新整理维度
            img_feat_raw = img_feat_raw.view(B, T, C, H, W)

            # 1. 先做空间池化 (GAP) nn.AdaptiveAvgPool2d((1,1))  或 直接 .mean()
            img_feat_pooled = img_feat_raw.mean(dim=(-1, -2))  # -> (B, T, C)
            # 2. 再做时间池化
            img_feat_aggregated = img_feat_pooled.mean(dim=1)  # -> (B, C)
        else:
            img_feat_aggregated = img_feat_raw.mean(dim=(-1, -2))  # 非TSM模型，直接做空间池化 -> (B, C)
            raise ValueError(f"@Moss Check this part.")

        # 此处 img_feat_aggregated 的形状已经是 (B, C)
        img_feat_final_adapted = self.image_adapter(img_feat_aggregated)

        # neck-Fusion
        if self.fusion_neck_model:
            fused_feat = self.fusion_neck_model(pose_feat_adapted, img_feat_final_adapted)
        else:
            fused_feat = torch.cat([pose_feat_adapted, img_feat_final_adapted], dim=1)

        return fused_feat

    def extract_feat(self, inputs: dict, test_mode=False) -> tuple:
        """
        提取姿态和图像特征. created by @Moss 20250605 11:45
        `inputs` from data_preprocessor are the (keypoints, images).
        """
        # test_mode: bool = False, data_samples = None
        # 从输入字典中获取列表数据
        if isinstance(inputs, dict):
            keypoint_lst = inputs['keypoint']  # List[Tensor], 每个元素shape: (clip, Member, 17, Frames, 3)
            imgs_lst = inputs['imgs']  # List[Tensor], 每个元素shape: (Frames=3, 3, 224, 224)

            keypoint_tensor = torch.stack(keypoint_lst, dim=0).squeeze(dim=1)
            # print(keypoint_tensor.shape)
            imgs_tensor = torch.stack(imgs_lst, dim=0).float() / 255
            # print(imgs_tensor.shape)
        else:
            keypoint_tensor, imgs_tensor = inputs

        # ===Data Preprocess: 2D Pose Sequence, Process RGB Images ===
        pose_data_pred = self.preprocess_pose_data(keypoint_tensor)     # (B, M, 17, 3)
        img_data_pred = self.preprocess_img_data(imgs_tensor)              # (B*T, C, H, W)

        # 提取融合特征的前向传播
        fused_feat = self.forward_once(pose_data_pred, img_data_pred)

        return fused_feat

    def loss(self, inputs: torch.Tensor, data_samples: SampleList, **kwargs) -> Dict[str, torch.Tensor]:
        """
        训练时的损失计算
        Args:
            inputs: 输入数据
            data_samples: 数据样本，包含标签等信息
        Returns:
            损失字典
        """
        # 1. 提取融合特征
        fused_feat = self.extract_feat(inputs, **kwargs)


        # 2. 将特征和标签信息传递给分类头计算损失
        #    BaseHead.loss 会接收 fused_feat，内部调用 forward，然后计算损失
        losses = self.cls_head.loss(fused_feat, data_samples, **kwargs)

        return losses

    def predict(self, inputs: dict, data_samples: SampleList, **kwargs) -> SampleList:
        """
        Predict results from a batch of inputs and data samples.
        This method is called when mode is 'predict'.
        """
        # 1. 提取融合特征
        fused_feat = self.extract_feat(inputs, **kwargs)

        # 2. 将特征传递给分类头进行前向传播，得到分数
        #    BaseHead.predict 会接收 fused_feat，内部调用 forward，然后打包结果
        predictions = self.cls_head.predict(fused_feat, data_samples, **kwargs)

        return predictions


def speed_test_BaseTSM():
    """
    将3帧数据叠加到通道，即为R50-Baes Channel
    """
    import time
    from functools import partial
    class mod_timer:
        """
        This is a Decorator of Timer module for func and code part:
        Usage:
        way1. @mod_timer
              def your_func():
                pass
        way2. with mod_timer() as t:
                your codes
        """

        def __init__(self, func=None):

            self.func = func
            self.start_code = None  # 片段
            self.end_code = None  # 片段

        def __call__(self, *args, **kwargs):
            _start = time.time()
            result = self.func(*args, **kwargs)
            _end = time.time()
            print(f"{self.func.__name__} run {_end - _start:.4f} s.")

            return result

        def __get__(self, obj):
            return partial(self.__call__, obj)

        def __enter__(self):
            self.start_code = time.time()
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            self.end_code = time.time()
            print(f"This part run {self.end_code - self.start_code:.4f} s.")

        def __repr__(self):
            return f"<Mod_timer used for {self.func.__name__}>"

    torch.manual_seed(2025)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(2025)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(device)

    # 定义输入特征维度
    batch_size = 1
    # T=num_frames, V=num_keypoints, C_in=dims_per_keypoint (e.g., 3 for x,y,score)
    num_frames_pose = 35
    num_keypoints = 17          # COCO
    pose_dims = 3
    dummy_pose_keypoints = torch.randn(batch_size, num_frames_pose, num_keypoints, pose_dims)       # (Batch, 35, 17, 3)
    # For ResNet (2D), input is (B_eff, C_rgb, H, W)
    img_channels = 3
    img_h, img_w = 224, 224
    frames = 3
    dummy_images = torch.randn(batch_size, frames, img_channels, img_h, img_w)
    # model param
    pose_backbone=dict(
        type='STGCN',  # 或其他姿态骨干网络 PoseC3D
        in_channels=3,          # xyc
        graph_cfg=dict(
            layout='coco',        # COCO 17个关键点
            mode='stgcn_spatial'),
        # edge_importance_weighting=True,
        # init_cfg=dict(type='Pretrained', checkpoint='path/to/stgcn_pretrained.pth')       # Add pretrained STGCN weights if available, e.g.
    )
    image_backbone = dict(
        type='MobileNetV2TSM',
        # --- MobileNetV2TSM 专属参数 ---
        num_segments=3,
        is_shift=True,
        shift_div=8,
        pretrained2d=True,  # 使用模型自带的2D预训练加载逻辑

        # --- 传递给底层 MobileNetV2 的参数 ---
        pretrained='mmcls://mobilenet_v2',  # 当 pretrained2d=True 时，init_weights会使用这个路径
        out_indices=(6,),
        frozen_stages=-1,
        norm_eval=False,

    )

    fusion_neck=dict(
        type='MultiModalFusionNeck',        # 适配器
        pose_feat_dim=256,              # ST-GCN 输出维度(num_class=256)
        img_feat_dim=320,              # TSM输出维度(stage 6为 )
        fusion_dim=512,
        fusion_type='cross_modal',  # 跨模态融合
        dropout=0.5
    )
    cls_head=dict(
        type='TwoFusionHead',              # 双流融合特征分类头
        num_classes=3,              # 类别
        in_channels=512,
        loss_cls=dict(
            type='CrossEntropyLoss',
            loss_weight=1.0,
            class_weight=[1.0, 2.0, 1.5])
    )

    # 创建随机输入张量
    inputs = (dummy_pose_keypoints.cuda(), dummy_images.cuda())

    model = MultiModalRecognizer(pose_backbone, image_backbone, fusion_neck, cls_head)
    model = model.to(device)

    model.eval()

    # 预热
    starter, ender = torch.cuda.Event(enable_timing=True), torch.cuda.Event(enable_timing=True)
    starter.record()
    for _ in range(50):
        # with mod_timer():
        _ = model.extract_feat(inputs, data_samples=None, test_mode=True)  # 调用 forward 方法
    ender.record()

    torch.cuda.synchronize()  # 同步GPU时间
    warm_up_time = starter.elapsed_time(ender)
    print("GPU warm up time:{:.2f}毫秒".format(warm_up_time))  # starter.elapsed_time单位：ms

    for _ in range(50):
        with mod_timer():
            model.extract_feat(inputs, data_samples=None, test_mode=True)  # 调用 forward 方法
    # 输出结果
    fused_feat = model.extract_feat(inputs, test_mode=True)  # 调用 forward 方法
    print("output shape:", fused_feat.shape, fused_feat)  # (batch, fusion_dim)





if __name__ == '__main__':
    # speed_test_BaseR50C6()

    speed_test_BaseTSM()


