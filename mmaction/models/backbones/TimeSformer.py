# -*-coding:utf-8-*-
"""
简化版TimeSformer适配器，更稳定的权重加载策略
created by @Moss 20250623
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import TimesformerModel, TimesformerConfig
import warnings
import os

# 过滤警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", message="TypedStorage is deprecated")

# 设置环境变量以控制下载行为
os.environ["HF_HUB_DISABLE_PROGRESS_BARS"] = "1"  # 禁用进度条（可选）


class SimpleAdaptiveTimeSformerEncoder(nn.Module):
    """
    简化版适配3帧输入的TimeSformer编码器
    使用更稳定的权重加载策略
    """

    def __init__(self,
                 pretrained_model_name: str = "facebook/timesformer-base-finetuned-k400",
                 num_frames: int = 3,
                 input_size: int = 256,
                 target_size: int = 224,
                 freeze_backbone: bool = False  # 是否冻结backbone参数
                 ):
        super().__init__()
        self.pretrained_model_name = pretrained_model_name
        self.num_frames = num_frames
        self.input_size = input_size
        self.target_size = target_size
        self.freeze_backbone = freeze_backbone

        # 直接加载预训练模型，然后适配输入
        print("加载预训练TimeSformer模型...")

        # 只下载必要的文件，避免重复下载
        try:
            self.model = TimesformerModel.from_pretrained(
                pretrained_model_name,
                torch_dtype=torch.float32,  # 明确指定数据类型
                local_files_only=False,  # 允许从网络下载
                use_safetensors=False  # 优先使用pytorch_model.bin
            )
        except Exception as e:
            print(f"尝试使用safetensors格式...")
            self.model = TimesformerModel.from_pretrained(pretrained_model_name)

        self.config = self.model.config

        # 如果需要冻结backbone
        if freeze_backbone:
            for param in self.model.parameters():
                param.requires_grad = False
            print("已冻结TimeSformer backbone参数")

        print(f"原始模型配置: 帧数={self.config.num_frames}, 图像尺寸={self.config.image_size}")
        print(f"适配后配置: 帧数={num_frames}, 输入尺寸={input_size}→{target_size}")

    def _resize_input(self, pixel_values):
        """
        将输入调整到模型期望的尺寸
        """
        if pixel_values.shape[-1] != self.target_size or pixel_values.shape[-2] != self.target_size:
            B, T, C, H, W = pixel_values.shape
            pixel_values = pixel_values.view(B * T, C, H, W)
            pixel_values = F.interpolate(
                pixel_values,
                size=(self.target_size, self.target_size),
                mode='bilinear',
                align_corners=False
            )
            pixel_values = pixel_values.view(B, T, C, self.target_size, self.target_size)
        return pixel_values

    def _adapt_frames(self, pixel_values):
        """
        适配帧数：如果输入帧数少于模型期望的帧数，进行帧重复或插值
        """
        B, T, C, H, W = pixel_values.shape
        target_frames = self.config.num_frames

        if T == target_frames:
            return pixel_values
        elif T < target_frames:
            # 如果输入帧数少，进行重复或插值
            if target_frames % T == 0:
                # 如果能整除，直接重复
                repeat_factor = target_frames // T
                pixel_values = pixel_values.repeat_interleave(repeat_factor, dim=1)
            else:
                # 使用插值
                pixel_values = pixel_values.permute(0, 2, 1, 3, 4)  # B, C, T, H, W
                pixel_values = F.interpolate(
                    pixel_values, size=(target_frames, H, W),
                    mode='trilinear', align_corners=False
                )
                pixel_values = pixel_values.permute(0, 2, 1, 3, 4)  # B, T, C, H, W
        else:
            # 如果输入帧数多，进行下采样
            indices = torch.linspace(0, T - 1, target_frames).long()
            pixel_values = pixel_values[:, indices]

        return pixel_values

    def forward(self, pixel_values: torch.Tensor):
        """
        前向传播函数

        Args:
            pixel_values: 输入视频张量，形状为 (batch_size, 3, 3, 256, 256)

        Returns:
            dict: 包含模型输出的字典
        """
        # 1. 调整空间尺寸
        pixel_values = self._resize_input(pixel_values)

        # 2. 适配时间维度（帧数）
        pixel_values = self._adapt_frames(pixel_values)

        # 3. 模型前向传播
        with torch.set_grad_enabled(not self.freeze_backbone):
            outputs = self.model(pixel_values=pixel_values)

        last_hidden_state = outputs.last_hidden_state

        # 4. 提取特征
        global_feature = last_hidden_state[:, 0, :]  # CLS token
        patch_features = last_hidden_state[:, 1:, :]  # patch tokens

        return {
            'global_feature': global_feature,
            'patch_features': patch_features,
            'last_hidden_state': last_hidden_state
        }

    @property
    def embed_dim(self):
        """返回模型的嵌入维度"""
        return self.config.hidden_size


# 使用示例
if __name__ == '__main__':
    print("=== 简化版TimeSformer适配器测试 ===")

    # 暂时抑制下载进度条（可选）
    import logging

    logging.getLogger("transformers").setLevel(logging.ERROR)

    # 实例化编码器
    vision_encoder = SimpleAdaptiveTimeSformerEncoder(
        pretrained_model_name="facebook/timesformer-base-finetuned-k400",
        num_frames=3,
        input_size=256,
        target_size=224,
        freeze_backbone=False  # 设为True可以冻结预训练权重
    )

    print(f"\n模型嵌入维度: {vision_encoder.embed_dim}")

    # 创建测试输入 (你的原始格式)
    batch_size = 2
    test_input = torch.randn(batch_size, 3, 3, 256, 256)
    print(f"输入形状: {test_input.shape}")

    # 前向传播测试
    vision_encoder.eval()
    with torch.no_grad():
        outputs = vision_encoder(test_input)

    # 检查输出
    print("\n=== 输出形状检查 ===")
    print(f"全局特征形状: {outputs['global_feature'].shape}")
    print(f"Patch特征形状: {outputs['patch_features'].shape}")
    print(f"完整隐藏状态形状: {outputs['last_hidden_state'].shape}")

    # 验证梯度
    if not vision_encoder.freeze_backbone:
        print(f"\n模型参数需要梯度: {any(p.requires_grad for p in vision_encoder.parameters())}")
    else:
        print(f"\n模型参数已冻结: {not any(p.requires_grad for p in vision_encoder.parameters())}")

    # 解释输出形状
    print(f"\n=== 输出形状解释 ===")
    original_frames = vision_encoder.config.num_frames
    patch_size = vision_encoder.config.patch_size
    target_size = vision_encoder.target_size

    patches_per_frame = (target_size // patch_size) ** 2
    total_patches = original_frames * patches_per_frame

    print(f"原始帧数: {original_frames}, 目标尺寸: {target_size}x{target_size}")
    print(f"Patch尺寸: {patch_size}x{patch_size}")
    print(f"每帧patches: {patches_per_frame}, 总patches: {total_patches}")
    print(f"序列长度: {total_patches} (patches) + 1 (CLS) = {total_patches + 1}")

    print("\n=== 测试完成 ===")
    print("注意: 如果看到后台下载，那是Hugging Face在缓存不同格式的模型文件，不影响使用。")