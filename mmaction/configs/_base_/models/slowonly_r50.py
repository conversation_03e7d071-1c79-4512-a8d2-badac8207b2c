# Copyright (c) OpenMMLab. All rights reserved.
from mmaction.models import (ActionDataPreprocessor, I3DHead, Recognizer3D,
                             ResNet3dSlowOnly)

model = dict(
    type=Recognizer3D,
    backbone=dict(
        type=ResNet3dSlowOnly,
        depth=50,
        pretrained='https://download.pytorch.org/models/resnet50-11ad3fa6.pth',
        lateral=False,
        conv1_kernel=(1, 7, 7),
        conv1_stride_t=1,
        pool1_stride_t=1,
        inflate=(0, 0, 1, 1),
        norm_eval=False),
    cls_head=dict(
        type=I3DHead,
        in_channels=2048,
        num_classes=400,
        spatial_type='avg',
        dropout_ratio=0.5,
        average_clips='prob'),
    data_preprocessor=dict(
        type=ActionDataPreprocessor,
        mean=[123.675, 116.28, 103.53],
        std=[58.395, 57.12, 57.375],
        format_shape='NCTHW'))
