# -*-coding:utf-8-*-
"""
fixed by Moss 2025/06/19 11:34

"""
import os
import re
import pickle
import time
from pathlib import Path
from tqdm import tqdm
import argparse

import numpy as np
import mmcv
from multiprocessing import Pool
from concurrent.futures import ThreadPoolExecutor


class SimplyResize:
    """Resize images to a specific size.

    Required keys are "img_shape", "modality", "imgs" (optional), "keypoint"
    (optional), added or modified keys are "imgs", "img_shape", "keep_ratio",
    "scale_factor", "lazy", "resize_size". Required keys in "lazy" is None,
    added or modified key is "interpolation".

    Args:
        scale (float | Tuple[int]): If keep_ratio is True, it serves as scaling
            factor or maximum size:
            If it is a float number, the image will be rescaled by this
            factor, else if it is a tuple of 2 integers, the image will
            be rescaled as large as possible within the scale.
            Otherwise, it serves as (w, h) of output size.
        keep_ratio (bool): If set to True, Images will be resized without
            changing the aspect ratio. Otherwise, it will resize images to a
            given size. Default: True.
        interpolation (str): Algorithm used for interpolation,
            accepted values are "nearest", "bilinear", "bicubic", "area",
            "lanczos". Default: "bilinear".
        lazy (bool): Determine whether to apply lazy operation. Default: False.
    """

    def __init__(self,
                 scale,
                 keep_ratio=True,
                 interpolation='bilinear',
                 lazy=False,
                 load_Resized_img=False,
                 use_padding=False,
                 pad_val=(128, 128, 128)):
        if isinstance(scale, float):
            if scale <= 0:
                raise ValueError(f'Invalid scale {scale}, must be positive.')
        elif isinstance(scale, tuple):
            max_long_edge = max(scale)
            max_short_edge = min(scale)
            if max_short_edge == -1:
                # assign np.inf to long edge for rescaling short edge later.
                scale = (np.inf, max_long_edge)
        else:
            raise TypeError(
                f'Scale must be float or tuple of int, but got {type(scale)}')
        self.scale = scale
        self.keep_ratio = keep_ratio
        self.interpolation = interpolation
        self.lazy = lazy
        self.load_Resized_img = load_Resized_img

        self.use_padding = use_padding
        self.pad_val = pad_val

    def _resize_imgs(self, imgs, new_w, new_h):
        """Static method for resizing keypoint."""
        return [
            mmcv.imresize(img, (new_w, new_h), interpolation=self.interpolation)
            for img in imgs
        ]

    @staticmethod
    def _resize_kps(kps, scale_factor):
        """Static method for resizing keypoint."""
        return kps * scale_factor

    @staticmethod
    def _box_resize(box, scale_factor):
        """Rescale the bounding boxes according to the scale_factor.

        Args:
            box (np.ndarray): The bounding boxes.
            scale_factor (np.ndarray): The scale factor used for rescaling.
        """
        assert len(scale_factor) == 2
        scale_factor = np.concatenate([scale_factor, scale_factor])
        return box * scale_factor


    def speed_readSample_lst(self, sample_pth):
        with ThreadPoolExecutor(max_workers=8) as executor:
            imgs = list(executor.map(self.load_img, sample_pth))
        return imgs


    def transform(self, results):
        """Performs the Resize augmentation.

        Args:
            results (dict): The resulting dict to be modified and passed
                to the next transform in pipeline.
        """
        pths = results['imgs_pth_Resize'] if self.load_Resized_img else results['imgs_pth']

        if self.load_Resized_img:
            # imgs = [mmcv.imread(img_pth) for img_pth in results['imgs_pth_Resize']]
            imgs = self.speed_readSample_lst(pths)

            results['imgs'] = imgs
            return results

        # 如果 'imgs' 不存在，但 'imgs_pth' 存在，则从路径加载原始图像
        if 'imgs' not in results and 'imgs_pth' in results:
            imgs = self.speed_readSample_lst(pths)
            if not len(imgs):
                raise ValueError(f"@Moss: Check {results['filename']}")

            results['imgs'] = imgs
            results['img_shape'] = imgs[0].shape[:2]            # 从第一张加载的图像中获取其原始形状 (高, 宽)


        if 'scale_factor' not in results:
            results['scale_factor'] = np.array([1, 1], dtype=np.float32)
        img_h, img_w = results['img_shape']

        if self.use_padding:
            # 增加pad需要的缩放比例
            scale_pad = min(self.scale[0] / img_w, self.scale[1] /img_h)
            p_w, p_h = int(img_w * scale_pad), int(img_h * scale_pad)
            pad_w = (self.scale[0] - p_w) // 2
            pad_h = (self.scale[1] - p_h) // 2

            if 'imgs' in results:
                results['imgs'] = self._resize_imgs(results['imgs'], p_w, p_h)
                results['imgs'] = [
                    mmcv.impad(a_im, shape=(self.scale[1], self.scale[0]), pad_val=self.pad_val)
                    for a_im in results['imgs']]
                results['img_shape'] = (self.scale[1], self.scale[0])           # h, w

        else:

            if self.keep_ratio:
                new_w, new_h = mmcv.rescale_size((img_w, img_h), self.scale)
            else:
                new_w, new_h = self.scale

            self.scale_factor = np.array([new_w / img_w, new_h / img_h], dtype=np.float32)

            results['img_shape'] = (new_h, new_w)
            results['keep_ratio'] = self.keep_ratio
            results['scale_factor'] = results['scale_factor'] * self.scale_factor

            if 'imgs' in results:
                results['imgs'] = self._resize_imgs(results['imgs'], new_w, new_h)


        return results

    @staticmethod
    def load_img(pth):
        return mmcv.imread(pth)

    def __repr__(self):
        repr_str = (f'{self.__class__.__name__}('
                    f'scale={self.scale}, keep_ratio={self.keep_ratio}, '
                    f'interpolation={self.interpolation}, '
                    f'lazy={self.lazy})')
        return repr_str



def mmengine_dump(obj, file_path, **kwargs):
    """
    参考mmengine.dump(anno, out) 将列表信息保存成pkl
    obj： 要序列化的对象
    file_path: 输出文件夹路径
    **kwargs: 传递给pickle.dump的额外参数
    """
    kwargs.setdefault('protocol', 2)        # 设置兼容老版本python
    with open(file_path, 'wb') as file:
        pickle.dump(obj, file, **kwargs)

    return


def mrlines(fname, sp='\n', only_path=False) -> list:
    """获取数据集的正确路径和标签"""
    f = open(fname).read().split(sp)
    while f != [] and f[-1] == '':
        f = f[:-1]

    if only_path:
        lines = [x.split()[0] for x in f]  # 将标签独立出来
    else:
        lines = [x.split() for x in f]  # 将标签独立出来
    return lines


def read_file_pkl(pkl_name:str) -> dict:
    """读取pkl信息"""
    if not Path(pkl_name).exists():
        return {}
    with open(pkl_name, 'rb') as fi:
        data = pickle.load(fi)
    return data


def pose_getResult(anno_in, base_info, pkl_name):
    """
    将读取到的样本 -> 需要的数据字典
        num_person = 1      # 默认单人
    """
    if not len(anno_in):
        return {}
    anno = dict()

    keypoint_lst = anno_in.get('pred_skpts')

    skpts = [skpt_ts[0][6:].view(17, 3)[:, :2] for skpt_ts in keypoint_lst if skpt_ts is not None]
    scores = [skpt_ts[0][6:].view(17, 3)[:, 2] for skpt_ts in keypoint_lst if skpt_ts is not None]

    anno['keypoint'] =np.stack(skpts, axis=0)           # (N, T, 17, 2)
    anno['keypoint_score'] =np.stack(scores, axis=0)           # (N, T, 17)

    # image_path
    base_name = Path(anno_in.get('sample_name')).stem
    images_path = [str(p) for p in Path(pkl_name).parent.glob(f'{base_name}*.jpg')]
    anno['imgs_pth'] = images_path

    # 其他信息汇总
    anno['img_shape'] = anno_in.get('img_shape')
    anno['original_shape'] = anno_in.get('oriVid_shape')
    anno['total_frames'] = len(skpts)
    anno['vid_frames'] = anno_in.get('total_frames')        # 这里补充1个视频部分的原始帧数(包含跟踪丢失和出画面的帧)
    anno.update(base_info)

    if args.Resize_Img:
        anno, imgs_pth_Resize = Add_imgs_pth_Resize(anno, images_path, use_padding=args.use_padding, write_local=True)



    del anno_in
    return anno


def Add_imgs_pth_Resize(anno, images_path, use_padding, write_local):
    """
    @Moss fixed 20250806
    Args:
        anno: 样本
        images_path:
        use_padding:
        write_local: args.Resize_Img, 为True时表示需要本地写入，本地已写入则跳过

    Returns:

    """
    # 判断pkl中是否存在本地路径，不存在则写入
    if anno.get('imgs_pth_Resize', None) is None:
        imgs_pth_Resize = []
        for i, img_pth in enumerate(anno['imgs_pth']):
            if str(args.video_list.parents[1]) in images_path[i]:
                save_pth = images_path[i].replace('pose_rgb', "pose_rgb_Resize")
                imgs_pth_Resize.append(save_pth)
        anno['imgs_pth_Resize'] = imgs_pth_Resize
    else:
        imgs_pth_Resize = anno['imgs_pth_Resize']

    # 是否需要本地写入 预处理后的img
    if write_local:
        # 确定需要写入后: 判断本地路径是否 样本存在
        if not Path(imgs_pth_Resize[0]).exists():
            if use_padding:
                anno = SimplyResize(scale=(224, 224), use_padding=True, pad_val=(128, 128, 128)).transform(anno)
            else:
                anno = SimplyResize(scale=(-1, 256)).transform(anno)
                anno = SimplyResize(scale=(224, 224), keep_ratio=False).transform(anno)

            for i, img in enumerate(anno['imgs']):
                mmcv.imwrite(img, imgs_pth_Resize[i])
            anno.pop('imgs', None)
        else:
            # 存在则跳过
            anno['img_shape'] = (224, 224)
            anno['scale_factor'] = np.array([1, 1], dtype=np.float32) * np.array([224 / 1920, 224 / 1080],dtype=np.float32)

    return anno, imgs_pth_Resize


def dump_all_label_ann(args) ->list:
    lines = mrlines(args.video_list)


    # * We set 'frame_dir' as the base name (w/o. suffix) of each video
    if len(lines[0]) == 2:
        annos = [dict(frame_dir=x[0].rsplit('.', 1)[0], filename=x[0], label=int(x[1].split('_')[0])) for x in lines]
    else:
        # 没有标签/标签错误，仅有pkl的情况，可能用于样本推理
        annos = [dict(frame_dir=x[0].rsplit('.', 1)[0], filename=x[0]) for x in lines]

    if args.output.exists():
        data_lst = read_file_pkl(args.output)
        all_labels_pth = mrlines(args.video_list, only_path=True)
        for i, anno in tqdm(enumerate(data_lst)):
            sample_name = anno['filename']
            if sample_name not in all_labels_pth:
                # all_labels_pth.remove(sample_name)
                del data_lst[i]         # 不在标签中的样本删除
                continue
            else:
                # 在标签中的样本 写入并判断/生成本地文件
                anno, _ = Add_imgs_pth_Resize(anno, images_path=anno['imgs_pth'], use_padding=args.use_padding, write_local=args.Resize_Img)
        results = data_lst
    else:
        results = []
        num_processes = os.cpu_count() - 1
        with Pool(processes=num_processes) as pool:
            for anno in tqdm(
                pool.imap(process_sing_item, reversed(annos)),
                total=len(annos),
                desc='speed deal pkls'
            ):
                results.append(anno)

    # for anno_info in tqdm(annos):
    #     pkl_name = anno_info.get('filename', 'None')
    #     anno_dict = read_file_pkl(pkl_name)
    #     anno = pose_getResult(anno_dict, anno_info, pkl_name)
    #
    #     results.append(anno)


    mmengine_dump(results, args.output)



    return results


def dump_trainVal_ann(args, annotations):
    train_lines = mrlines(args.tain_txt_path)
    val_lines = mrlines(args.val_txt_path)

    split = dict()
    split['train'] = [x[0] for x in train_lines]          # file_name_跑道_frame.pkl
    split['val'] = [x[0] for x in val_lines]

    mmengine_dump(dict(split=split, annotations=annotations), args.out_pose_pkl)

    return


def modeified_one(lines, target_var, new_value):
    modified = False
    for i, line in enumerate(lines):
        # 使用正则表达式匹配变量赋值行
        if re.match(rf'^{target_var}\s*=\s*.*$', line):
            # 替换变量值
            if isinstance(new_value, str):
                lines[i] = f"{target_var} = '{new_value}'\n"
            elif isinstance(new_value, int):
                pattern = r'{}s*=\s*\d+'.format(re.escape(target_var))
                replacement = r'{} = {}'.format(target_var, new_value)
                lines[i] = re.sub(pattern, replacement, lines[i])

            modified = True
            break

    if not modified:
        print(f"Moss: Do not Found {target_var}，can not be fixed。")

    return lines


def modify_config_file(file_path, target_var, new_value, new_file_path):
    """
    修改 slowonly_r50_sport-keypoint.py 文件中的变量值

    Args:
        file_path (str): config.py 文件的路径
        target_var (list): 要修改的变量名 [Test_ann_file, test_batch]
        new_value (list): 新的变量值 [val1,val2]
    """
    # try:
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    # 修改 列表中的变量值
    for target_one, new_one in zip(target_var, new_value):
        lines = modeified_one(lines, target_one, new_one)

    # 修改后的内容写到 新文件中
    with open(new_file_path, 'w', encoding='utf-8') as file:
        file.writelines(lines)

    assert Path(args.new_file_path).exists(), '@Moss : ???'

    print(f"@Moss: successs fix the value of {target_var} to '{new_value}'。")

    # except FileNotFoundError:
    #     print(f"错误: 文件 {file_path} 不存在。")
    # except Exception as e:
    #     print(f"错误: 修改文件时发生异常 - {e}")


def process_sing_item(anno_info):
    pkl_name = anno_info.get('filename', 'None')
    anno_dict = read_file_pkl(pkl_name)
    anno = pose_getResult(anno_dict, anno_info, pkl_name)

    return anno


def parse_args():
    parser = argparse.ArgumentParser(
        description='Generate 2D pose-RGB annotations for a custom fusion dataset')

    parser.add_argument('--video-list', type=str, help='the list of source videos Fusion 目录',
                        default="/root/share175/sport_datas/sit_up/classify_cheating/train/Fusion_datasets")
                        # default="/root/share175/sport_test/sit_up/classify_cheat/Fusion_datasets")
                        # default="/root/share175/sport_datas/sit_up/classify_cheating/train/pose_rgb/2_headErr/Fusion_datasets")

    parser.add_argument('--Resize_Img', type=bool, default=True, help='对数据 进行本地Resize预处理')
    parser.add_argument('--use_padding', type=bool, default=True, help='对样本 进行padding策略, 仅在Resize_Img=True 有效')

    parser.add_argument('--tain_txt', type=str, help='默认在video_list同级', default='train_label.txt')
    parser.add_argument('--val_txt', type=str, help='默认在video_list同级', default='val_label.txt')

    parser.add_argument('--output', type=str, help='output pickle name, 默认存储在video_list同级', default='None')
    parser.add_argument('--out_pose_pkl', type=str, help='output pickle name, 默认存储在video_list同级', default='None')

    parser.add_argument('--TestMod', type=str, default=False, help='测试集模式')

    args = parser.parse_args()

    if isinstance(args.TestMod, str):
        args.TestMod = args.TestMod.lower() == 'true'  # True 或 true

    if args.TestMod:
        pkl_names = ['test_all_label.txt', 'test_all_label_ann.pkl', 'test_trainval_yolopose.pkl']
        args.config = "../configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py"
        args.new_file_path = Path(args.video_list) / Path(args.config).name
    else:
        pkl_names = ['all_label.txt', 'all_label_ann.pkl', 'trainval_yolopose.pkl']

    args.video_list = Path(args.video_list) / pkl_names[0]

    if not Path(args.output).is_file():
        args.output = Path(args.video_list).parent / pkl_names[1]
    if not Path(args.out_pose_pkl).is_file():
        args.out_pose_pkl = Path(args.video_list).parent / pkl_names[2]

    args.tain_txt_path = Path(args.video_list).parent / args.tain_txt
    args.val_txt_path = Path(args.video_list).parent / args.val_txt

    return args


if __name__ == '__main__':
    args = parse_args()

    # Step：生成大Pkl
    annotations = dump_all_label_ann(args)
    print('1.Generate all_label_ann.pkl', end=';\n')

    if not args.TestMod:
        # Step: 生成训练、验证的pkl
        dump_trainVal_ann(args, annotations)
        print('2.Generate trainval_yolopose.pkl success!')
    else:
        modify_config_file(file_path=args.config, target_var=['Test_ann_file'],
                           new_value=[str(args.output)], new_file_path=args.new_file_path)
        print(f"@Moss: we fixed {args.config}:\n Test_ann_file, become {args.new_file_path}.")

